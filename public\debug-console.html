<!DOCTYPE html>
<html>
<head>
    <title>Debug Console</title>
    <meta charset="utf-8">
    <style>
        body { font-family: monospace; margin: 20px; background: #1a1a1a; color: #00ff00; }
        .container { max-width: 800px; margin: 0 auto; }
        .log { margin: 5px 0; padding: 5px; border-left: 3px solid #00ff00; }
        .error { color: #ff4444; border-left-color: #ff4444; }
        .warning { color: #ffaa00; border-left-color: #ffaa00; }
        .info { color: #4488ff; border-left-color: #4488ff; }
        button { padding: 10px 20px; margin: 5px; background: #333; color: #00ff00; border: 1px solid #00ff00; cursor: pointer; }
        button:hover { background: #555; }
        #console { height: 400px; overflow-y: auto; background: #000; padding: 10px; border: 1px solid #333; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔍 WIDDX Debug Console</h1>
        
        <button onclick="testAPI()">Test API Direct</button>
        <button onclick="testWIDDX()">Test WIDDX Function</button>
        <button onclick="checkWIDDX()">Check WIDDX Object</button>
        <button onclick="clearConsole()">Clear</button>
        
        <div id="console"></div>
    </div>

    <script>
        function log(message, type = 'log') {
            const console = document.getElementById('console');
            const div = document.createElement('div');
            div.className = `log ${type}`;
            div.textContent = new Date().toLocaleTimeString() + ' - ' + message;
            console.appendChild(div);
            console.scrollTop = console.scrollHeight;
            
            // Also log to browser console
            if (type === 'error') {
                window.console.error(message);
            } else if (type === 'warning') {
                window.console.warn(message);
            } else {
                window.console.log(message);
            }
        }

        async function testAPI() {
            log('🧪 Testing API directly...', 'info');
            
            try {
                const response = await fetch('/api/chat', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Accept': 'application/json'
                    },
                    body: JSON.stringify({
                        message: 'مرحبا',
                        language: 'ar',
                        features: {}
                    })
                });
                
                log(`Response status: ${response.status}`, 'info');
                log(`Response headers: ${JSON.stringify([...response.headers.entries()])}`, 'info');
                
                const data = await response.json();
                log(`Response data: ${JSON.stringify(data, null, 2)}`, 'log');
                
                if (data.success) {
                    log(`✅ API works! Message: ${data.message}`, 'log');
                } else {
                    log(`❌ API failed: ${data.error}`, 'error');
                }
                
            } catch (error) {
                log(`❌ API request failed: ${error.message}`, 'error');
                log(`Error stack: ${error.stack}`, 'error');
            }
        }

        async function testWIDDX() {
            log('🧪 Testing WIDDX function...', 'info');
            
            if (window.widdxModern) {
                log('✅ WIDDX object found', 'log');
                
                if (typeof window.widdxModern.sendToAPI === 'function') {
                    log('✅ sendToAPI function found', 'log');
                    
                    try {
                        const result = await window.widdxModern.sendToAPI({
                            message: 'مرحبا',
                            language: 'ar',
                            features: {}
                        });
                        
                        log(`✅ WIDDX sendToAPI result: ${JSON.stringify(result, null, 2)}`, 'log');
                        
                    } catch (error) {
                        log(`❌ WIDDX sendToAPI failed: ${error.message}`, 'error');
                    }
                } else {
                    log('❌ sendToAPI function not found', 'error');
                }
                
                if (typeof window.widdxModern.submitMessage === 'function') {
                    log('✅ submitMessage function found', 'log');
                } else {
                    log('❌ submitMessage function not found', 'error');
                }
                
            } else {
                log('❌ WIDDX object not found', 'error');
            }
        }

        function checkWIDDX() {
            log('🔍 Checking WIDDX object...', 'info');
            
            if (window.widdxModern) {
                log('✅ window.widdxModern exists', 'log');
                log(`WIDDX properties: ${Object.keys(window.widdxModern).join(', ')}`, 'info');
                
                // Check specific functions
                const functions = ['sendToAPI', 'submitMessage', 'intelligentFallback', 'simulateAPICall'];
                functions.forEach(func => {
                    if (typeof window.widdxModern[func] === 'function') {
                        log(`✅ ${func} function exists`, 'log');
                    } else {
                        log(`❌ ${func} function missing`, 'error');
                    }
                });
                
                // Check config
                if (window.widdxModern.config) {
                    log(`Config: ${JSON.stringify(window.widdxModern.config, null, 2)}`, 'info');
                } else {
                    log('❌ Config missing', 'error');
                }
                
            } else {
                log('❌ window.widdxModern does not exist', 'error');
                log('Available window objects:', 'info');
                Object.keys(window).filter(key => key.toLowerCase().includes('widdx')).forEach(key => {
                    log(`Found: window.${key}`, 'info');
                });
            }
        }

        function clearConsole() {
            document.getElementById('console').innerHTML = '';
        }

        // Auto-run checks on load
        window.addEventListener('load', () => {
            log('🚀 Debug console loaded', 'info');
            setTimeout(() => {
                checkWIDDX();
            }, 2000);
        });

        // Capture console logs
        const originalLog = console.log;
        const originalError = console.error;
        const originalWarn = console.warn;

        console.log = function(...args) {
            log(args.join(' '), 'log');
            originalLog.apply(console, args);
        };

        console.error = function(...args) {
            log(args.join(' '), 'error');
            originalError.apply(console, args);
        };

        console.warn = function(...args) {
            log(args.join(' '), 'warning');
            originalWarn.apply(console, args);
        };
    </script>
</body>
</html>
