<?php

echo "🧪 Testing WIDDX API...\n\n";

// Test 1: Health Check
echo "1️⃣ Testing Health Check:\n";
$healthResponse = file_get_contents('http://127.0.0.1:8000/api/chat/health');
echo "Response: " . $healthResponse . "\n\n";

// Test 2: Chat API with Arabic
echo "2️⃣ Testing Chat API with Arabic:\n";
$chatData = json_encode([
    'message' => 'مرحبا',
    'language' => 'ar',
    'features' => []
]);

$context = stream_context_create([
    'http' => [
        'method' => 'POST',
        'header' => 'Content-Type: application/json',
        'content' => $chatData
    ]
]);

$chatResponse = file_get_contents('http://127.0.0.1:8000/api/chat', false, $context);
echo "Response: " . $chatResponse . "\n\n";

// Test 3: Chat API with English
echo "3️⃣ Testing Chat API with English:\n";
$chatDataEn = json_encode([
    'message' => 'hello',
    'language' => 'en',
    'features' => []
]);

$contextEn = stream_context_create([
    'http' => [
        'method' => 'POST',
        'header' => 'Content-Type: application/json',
        'content' => $chatDataEn
    ]
]);

$chatResponseEn = file_get_contents('http://127.0.0.1:8000/api/chat', false, $contextEn);
echo "Response: " . $chatResponseEn . "\n\n";

echo "✅ API Testing Complete!\n";
