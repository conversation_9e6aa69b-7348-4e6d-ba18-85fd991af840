/**
 * Quick Test for WIDDX Interface
 * Tests basic functionality immediately
 */

console.log('🧪 Quick Test Starting...');

// Test 1: Check if elements exist
function testElements() {
    console.log('🔍 Testing DOM elements...');
    
    const elements = [
        'chat-form',
        'message-input', 
        'send-button',
        'messages-container'
    ];
    
    let passed = 0;
    elements.forEach(id => {
        const element = document.getElementById(id);
        if (element) {
            console.log(`✅ ${id}: Found`);
            passed++;
        } else {
            console.log(`❌ ${id}: Not found`);
        }
    });
    
    console.log(`Elements test: ${passed}/${elements.length} passed`);
    return passed === elements.length;
}

// Test 2: Check if WIDDX instance exists
function testWiddxInstance() {
    console.log('🔍 Testing WIDDX instance...');
    
    if (window.widdxModern) {
        console.log('✅ WIDDX instance: Found');
        
        // Test methods
        const methods = ['submitMessage', 'toggleFeature', 'addMessage'];
        let methodsPassed = 0;
        
        methods.forEach(method => {
            if (typeof window.widdxModern[method] === 'function') {
                console.log(`✅ Method ${method}: Available`);
                methodsPassed++;
            } else {
                console.log(`❌ Method ${method}: Not available`);
            }
        });
        
        console.log(`Methods test: ${methodsPassed}/${methods.length} passed`);
        return methodsPassed === methods.length;
    } else {
        console.log('❌ WIDDX instance: Not found');
        return false;
    }
}

// Test 3: Test API endpoint
async function testAPI() {
    console.log('🔍 Testing API endpoint...');
    
    try {
        const response = await fetch('/api/chat/health');
        if (response.ok) {
            const data = await response.json();
            console.log('✅ API Health:', data);
            return true;
        } else {
            console.log('❌ API Health: Failed', response.status);
            return false;
        }
    } catch (error) {
        console.log('❌ API Health: Error', error.message);
        return false;
    }
}

// Test 4: Test message sending
async function testMessageSending() {
    console.log('🔍 Testing message sending...');
    
    const messageInput = document.getElementById('message-input');
    const sendButton = document.getElementById('send-button');
    
    if (!messageInput || !sendButton) {
        console.log('❌ Message sending: Elements not found');
        return false;
    }
    
    // Test input change
    messageInput.value = 'Test message';
    messageInput.dispatchEvent(new Event('input'));
    
    // Check if send button is enabled
    if (!sendButton.disabled) {
        console.log('✅ Message sending: Send button enabled with text');
        
        // Clear input
        messageInput.value = '';
        messageInput.dispatchEvent(new Event('input'));
        
        if (sendButton.disabled) {
            console.log('✅ Message sending: Send button disabled when empty');
            return true;
        } else {
            console.log('❌ Message sending: Send button should be disabled when empty');
            return false;
        }
    } else {
        console.log('❌ Message sending: Send button not enabled with text');
        return false;
    }
}

// Test 5: Test Enter key
function testEnterKey() {
    console.log('🔍 Testing Enter key...');
    
    const messageInput = document.getElementById('message-input');
    if (!messageInput) {
        console.log('❌ Enter key: Input not found');
        return false;
    }
    
    messageInput.value = 'Test Enter key';
    messageInput.focus();
    
    // Simulate Enter key
    const enterEvent = new KeyboardEvent('keydown', {
        key: 'Enter',
        bubbles: true
    });
    
    messageInput.dispatchEvent(enterEvent);
    console.log('✅ Enter key: Event dispatched');
    return true;
}

// Run all tests
async function runQuickTests() {
    console.log('🚀 Running Quick Tests...');
    
    const results = {
        elements: testElements(),
        instance: testWiddxInstance(),
        api: await testAPI(),
        messaging: await testMessageSending(),
        enterKey: testEnterKey()
    };
    
    const passed = Object.values(results).filter(r => r).length;
    const total = Object.keys(results).length;
    
    console.log('\n📊 QUICK TEST RESULTS:');
    console.log('======================');
    Object.entries(results).forEach(([test, result]) => {
        console.log(`${result ? '✅' : '❌'} ${test}: ${result ? 'PASS' : 'FAIL'}`);
    });
    
    console.log(`\n🎯 Overall: ${passed}/${total} tests passed (${((passed/total)*100).toFixed(1)}%)`);
    
    if (passed === total) {
        console.log('🎉 All tests passed! Interface is working correctly.');
        
        // Show success notification
        if (window.widdxModern && typeof window.widdxModern.showNotification === 'function') {
            window.widdxModern.showNotification('All tests passed! 🎉', 'success');
        }
    } else {
        console.log('⚠️ Some tests failed. Check the console for details.');
        
        // Show warning notification
        if (window.widdxModern && typeof window.widdxModern.showNotification === 'function') {
            window.widdxModern.showNotification(`${passed}/${total} tests passed`, 'warning');
        }
    }
    
    return results;
}

// Auto-run tests after a delay
setTimeout(() => {
    runQuickTests();
}, 2000);

// Export for manual testing
window.runQuickTests = runQuickTests;
