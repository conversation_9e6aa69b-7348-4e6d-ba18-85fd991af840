<!DOCTYPE html>
<html lang="en" class="dark">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>WIDDX AI - Settings</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script>
        tailwind.config = {
            darkMode: 'class',
            theme: {
                extend: {
                    colors: {
                        'widdx': {
                            '50': '#eff6ff',
                            '500': '#3B82F6',
                            '600': '#2563EB',
                            '900': '#1e3a8a'
                        }
                    }
                }
            }
        }
    </script>
</head>
<body class="bg-gray-50 dark:bg-gray-900 text-gray-900 dark:text-white min-h-screen">
    <!-- Header -->
    <header class="bg-white dark:bg-gray-800 shadow-sm border-b border-gray-200 dark:border-gray-700">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between items-center h-16">
                <div class="flex items-center">
                    <a href="/" class="flex items-center space-x-3">
                        <div class="w-8 h-8 bg-gradient-to-br from-widdx-500 to-widdx-600 rounded-lg flex items-center justify-center">
                            <span class="text-white font-bold text-sm">W</span>
                        </div>
                        <span class="text-xl font-bold">WIDDX AI</span>
                    </a>
                </div>
                <div class="flex items-center space-x-4">
                    <a href="/" class="text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white">
                        <i class="fas fa-arrow-left mr-2"></i>Back to Chat
                    </a>
                    <button id="theme-toggle" class="p-2 text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200">
                        <i class="fas fa-moon dark:hidden"></i>
                        <i class="hidden fas fa-sun dark:inline"></i>
                    </button>
                </div>
            </div>
        </div>
    </header>

    <!-- Main Content -->
    <main class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div class="mb-8">
            <h1 class="text-3xl font-bold text-gray-900 dark:text-white">Settings</h1>
            <p class="mt-2 text-gray-600 dark:text-gray-400">Customize your WIDDX AI experience</p>
        </div>

        <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
            <!-- Settings Navigation -->
            <div class="lg:col-span-1">
                <nav class="space-y-2">
                    <a href="#general" class="settings-nav-item active block px-4 py-2 text-sm font-medium rounded-lg bg-widdx-500 text-white">
                        <i class="fas fa-cog mr-3"></i>General
                    </a>
                    <a href="#appearance" class="settings-nav-item block px-4 py-2 text-sm font-medium rounded-lg text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700">
                        <i class="fas fa-palette mr-3"></i>Appearance
                    </a>
                    <a href="#features" class="settings-nav-item block px-4 py-2 text-sm font-medium rounded-lg text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700">
                        <i class="fas fa-magic mr-3"></i>Features
                    </a>
                    <a href="#privacy" class="settings-nav-item block px-4 py-2 text-sm font-medium rounded-lg text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700">
                        <i class="fas fa-shield-alt mr-3"></i>Privacy
                    </a>
                    <a href="#advanced" class="settings-nav-item block px-4 py-2 text-sm font-medium rounded-lg text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700">
                        <i class="fas fa-cogs mr-3"></i>Advanced
                    </a>
                </nav>
            </div>

            <!-- Settings Content -->
            <div class="lg:col-span-2">
                <!-- General Settings -->
                <div id="general" class="settings-section">
                    <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6">
                        <h2 class="text-xl font-semibold mb-6">General Settings</h2>
                        
                        <div class="space-y-6">
                            <div>
                                <label class="block text-sm font-medium mb-2">Language</label>
                                <select id="language-select" class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white">
                                    <option value="en">🇺🇸 English</option>
                                    <option value="ar">🇸🇦 العربية</option>
                                    <option value="es">🇪🇸 Español</option>
                                    <option value="fr">🇫🇷 Français</option>
                                    <option value="de">🇩🇪 Deutsch</option>
                                    <option value="zh">🇨🇳 中文</option>
                                    <option value="ja">🇯🇵 日本語</option>
                                    <option value="ko">🇰🇷 한국어</option>
                                    <option value="ru">🇷🇺 Русский</option>
                                </select>
                            </div>

                            <div class="flex items-center justify-between">
                                <div>
                                    <label class="block text-sm font-medium">Notifications</label>
                                    <p class="text-sm text-gray-600 dark:text-gray-400">Receive notifications for responses and updates</p>
                                </div>
                                <label class="relative inline-flex items-center cursor-pointer">
                                    <input type="checkbox" id="notifications-toggle" class="sr-only peer" checked>
                                    <div class="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-widdx-300 dark:peer-focus:ring-widdx-800 rounded-full peer dark:bg-gray-700 peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all dark:border-gray-600 peer-checked:bg-widdx-600"></div>
                                </label>
                            </div>

                            <div class="flex items-center justify-between">
                                <div>
                                    <label class="block text-sm font-medium">Auto-save Conversations</label>
                                    <p class="text-sm text-gray-600 dark:text-gray-400">Automatically save your chat history</p>
                                </div>
                                <label class="relative inline-flex items-center cursor-pointer">
                                    <input type="checkbox" id="auto-save-toggle" class="sr-only peer" checked>
                                    <div class="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-widdx-300 dark:peer-focus:ring-widdx-800 rounded-full peer dark:bg-gray-700 peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all dark:border-gray-600 peer-checked:bg-widdx-600"></div>
                                </label>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Appearance Settings -->
                <div id="appearance" class="settings-section hidden">
                    <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6">
                        <h2 class="text-xl font-semibold mb-6">Appearance</h2>
                        
                        <div class="space-y-6">
                            <div>
                                <label class="block text-sm font-medium mb-3">Theme</label>
                                <div class="grid grid-cols-2 gap-4">
                                    <label class="cursor-pointer">
                                        <input type="radio" name="theme" value="light" class="sr-only peer">
                                        <div class="p-4 border-2 border-gray-200 dark:border-gray-600 rounded-lg peer-checked:border-widdx-500 peer-checked:bg-widdx-50 dark:peer-checked:bg-widdx-900/20">
                                            <div class="flex items-center space-x-3">
                                                <div class="w-6 h-6 bg-white border border-gray-300 rounded"></div>
                                                <span>Light</span>
                                            </div>
                                        </div>
                                    </label>
                                    <label class="cursor-pointer">
                                        <input type="radio" name="theme" value="dark" class="sr-only peer" checked>
                                        <div class="p-4 border-2 border-gray-200 dark:border-gray-600 rounded-lg peer-checked:border-widdx-500 peer-checked:bg-widdx-50 dark:peer-checked:bg-widdx-900/20">
                                            <div class="flex items-center space-x-3">
                                                <div class="w-6 h-6 bg-gray-800 border border-gray-600 rounded"></div>
                                                <span>Dark</span>
                                            </div>
                                        </div>
                                    </label>
                                </div>
                            </div>

                            <div class="flex items-center justify-between">
                                <div>
                                    <label class="block text-sm font-medium">Compact Mode</label>
                                    <p class="text-sm text-gray-600 dark:text-gray-400">Use a more compact interface layout</p>
                                </div>
                                <label class="relative inline-flex items-center cursor-pointer">
                                    <input type="checkbox" id="compact-mode-toggle" class="sr-only peer">
                                    <div class="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-widdx-300 dark:peer-focus:ring-widdx-800 rounded-full peer dark:bg-gray-700 peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all dark:border-gray-600 peer-checked:bg-widdx-600"></div>
                                </label>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Features Settings -->
                <div id="features" class="settings-section hidden">
                    <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6">
                        <h2 class="text-xl font-semibold mb-6">Features</h2>
                        
                        <div class="space-y-6">
                            <div class="flex items-center justify-between">
                                <div>
                                    <label class="block text-sm font-medium">Auto Feature Detection</label>
                                    <p class="text-sm text-gray-600 dark:text-gray-400">Automatically activate features based on your requests</p>
                                </div>
                                <label class="relative inline-flex items-center cursor-pointer">
                                    <input type="checkbox" id="auto-features-toggle" class="sr-only peer" checked>
                                    <div class="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-widdx-300 dark:peer-focus:ring-widdx-800 rounded-full peer dark:bg-gray-700 peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all dark:border-gray-600 peer-checked:bg-widdx-600"></div>
                                </label>
                            </div>

                            <div class="flex items-center justify-between">
                                <div>
                                    <label class="block text-sm font-medium">Voice Input</label>
                                    <p class="text-sm text-gray-600 dark:text-gray-400">Enable voice input for messages</p>
                                </div>
                                <label class="relative inline-flex items-center cursor-pointer">
                                    <input type="checkbox" id="voice-input-toggle" class="sr-only peer">
                                    <div class="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-widdx-300 dark:peer-focus:ring-widdx-800 rounded-full peer dark:bg-gray-700 peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all dark:border-gray-600 peer-checked:bg-widdx-600"></div>
                                </label>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Privacy Settings -->
                <div id="privacy" class="settings-section hidden">
                    <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6">
                        <h2 class="text-xl font-semibold mb-6">Privacy & Security</h2>
                        
                        <div class="space-y-6">
                            <div class="flex items-center justify-between">
                                <div>
                                    <label class="block text-sm font-medium">Data Collection</label>
                                    <p class="text-sm text-gray-600 dark:text-gray-400">Allow anonymous usage data collection for improvements</p>
                                </div>
                                <label class="relative inline-flex items-center cursor-pointer">
                                    <input type="checkbox" id="data-collection-toggle" class="sr-only peer">
                                    <div class="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-widdx-300 dark:peer-focus:ring-widdx-800 rounded-full peer dark:bg-gray-700 peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all dark:border-gray-600 peer-checked:bg-widdx-600"></div>
                                </label>
                            </div>

                            <div>
                                <button class="w-full px-4 py-2 text-sm text-red-600 dark:text-red-400 border border-red-300 dark:border-red-600 rounded-lg hover:bg-red-50 dark:hover:bg-red-900/20">
                                    <i class="fas fa-trash mr-2"></i>Clear All Data
                                </button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Advanced Settings -->
                <div id="advanced" class="settings-section hidden">
                    <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6">
                        <h2 class="text-xl font-semibold mb-6">Advanced</h2>
                        
                        <div class="space-y-6">
                            <div class="flex items-center justify-between">
                                <div>
                                    <label class="block text-sm font-medium">Developer Mode</label>
                                    <p class="text-sm text-gray-600 dark:text-gray-400">Enable advanced debugging and logging</p>
                                </div>
                                <label class="relative inline-flex items-center cursor-pointer">
                                    <input type="checkbox" id="developer-mode-toggle" class="sr-only peer">
                                    <div class="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-widdx-300 dark:peer-focus:ring-widdx-800 rounded-full peer dark:bg-gray-700 peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all dark:border-gray-600 peer-checked:bg-widdx-600"></div>
                                </label>
                            </div>

                            <div>
                                <button class="w-full px-4 py-2 text-sm text-gray-700 dark:text-gray-300 border border-gray-300 dark:border-gray-600 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700">
                                    <i class="fas fa-download mr-2"></i>Export Settings
                                </button>
                            </div>

                            <div>
                                <button class="w-full px-4 py-2 text-sm text-gray-700 dark:text-gray-300 border border-gray-300 dark:border-gray-600 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700">
                                    <i class="fas fa-upload mr-2"></i>Import Settings
                                </button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Save Button -->
                <div class="mt-8 flex justify-end space-x-4">
                    <button id="reset-all-settings" class="px-6 py-2 text-sm text-gray-700 dark:text-gray-300 border border-gray-300 dark:border-gray-600 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700">
                        Reset All
                    </button>
                    <button id="save-all-settings" class="px-6 py-2 text-sm text-white bg-widdx-500 rounded-lg hover:bg-widdx-600">
                        Save Changes
                    </button>
                </div>
            </div>
        </div>
    </main>

    <script>
        // Settings navigation
        document.querySelectorAll('.settings-nav-item').forEach(item => {
            item.addEventListener('click', (e) => {
                e.preventDefault();
                const target = e.target.getAttribute('href').substring(1);
                
                // Update navigation
                document.querySelectorAll('.settings-nav-item').forEach(nav => {
                    nav.classList.remove('active', 'bg-widdx-500', 'text-white');
                    nav.classList.add('text-gray-700', 'dark:text-gray-300');
                });
                e.target.classList.add('active', 'bg-widdx-500', 'text-white');
                e.target.classList.remove('text-gray-700', 'dark:text-gray-300');
                
                // Update content
                document.querySelectorAll('.settings-section').forEach(section => {
                    section.classList.add('hidden');
                });
                document.getElementById(target).classList.remove('hidden');
            });
        });

        // Theme toggle
        document.getElementById('theme-toggle').addEventListener('click', () => {
            const html = document.documentElement;
            const isDark = html.classList.contains('dark');
            
            if (isDark) {
                html.classList.remove('dark');
                localStorage.setItem('theme', 'light');
            } else {
                html.classList.add('dark');
                localStorage.setItem('theme', 'dark');
            }
        });

        // Save settings
        document.getElementById('save-all-settings').addEventListener('click', () => {
            const settings = {
                language: document.getElementById('language-select').value,
                notifications: document.getElementById('notifications-toggle').checked,
                autoSave: document.getElementById('auto-save-toggle').checked,
                compactMode: document.getElementById('compact-mode-toggle').checked,
                autoFeatures: document.getElementById('auto-features-toggle').checked,
                voiceInput: document.getElementById('voice-input-toggle').checked,
                dataCollection: document.getElementById('data-collection-toggle').checked,
                developerMode: document.getElementById('developer-mode-toggle').checked,
                theme: document.querySelector('input[name="theme"]:checked').value
            };
            
            localStorage.setItem('widdx_settings', JSON.stringify(settings));
            alert('Settings saved successfully!');
        });

        // Reset settings
        document.getElementById('reset-all-settings').addEventListener('click', () => {
            if (confirm('Are you sure you want to reset all settings?')) {
                localStorage.removeItem('widdx_settings');
                location.reload();
            }
        });

        // Load settings on page load
        window.addEventListener('load', () => {
            const savedSettings = localStorage.getItem('widdx_settings');
            if (savedSettings) {
                const settings = JSON.parse(savedSettings);
                
                if (settings.language) document.getElementById('language-select').value = settings.language;
                if (settings.notifications !== undefined) document.getElementById('notifications-toggle').checked = settings.notifications;
                if (settings.autoSave !== undefined) document.getElementById('auto-save-toggle').checked = settings.autoSave;
                if (settings.compactMode !== undefined) document.getElementById('compact-mode-toggle').checked = settings.compactMode;
                if (settings.autoFeatures !== undefined) document.getElementById('auto-features-toggle').checked = settings.autoFeatures;
                if (settings.voiceInput !== undefined) document.getElementById('voice-input-toggle').checked = settings.voiceInput;
                if (settings.dataCollection !== undefined) document.getElementById('data-collection-toggle').checked = settings.dataCollection;
                if (settings.developerMode !== undefined) document.getElementById('developer-mode-toggle').checked = settings.developerMode;
                if (settings.theme) document.querySelector(`input[name="theme"][value="${settings.theme}"]`).checked = true;
            }
        });
    </script>
</body>
</html>
<?php /**PATH C:\Users\<USER>\Desktop\widdx-ai\resources\views/settings.blade.php ENDPATH**/ ?>