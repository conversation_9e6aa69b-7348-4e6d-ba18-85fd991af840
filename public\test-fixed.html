<!DOCTYPE html>
<html lang="en" class="dark">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>WIDDX AI - Test Fixed</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            darkMode: 'class',
            theme: {
                extend: {
                    colors: {
                        'widdx': {
                            '500': '#3B82F6',
                            '600': '#2563EB'
                        }
                    }
                }
            }
        }
    </script>
    <style>
        .animate-slide-in {
            animation: slideIn 0.3s ease-out;
        }
        @keyframes slideIn {
            from { opacity: 0; transform: translateY(10px); }
            to { opacity: 1; transform: translateY(0); }
        }
    </style>
</head>
<body class="bg-gray-50 dark:bg-gray-900 text-gray-900 dark:text-white min-h-screen">
    <div class="container mx-auto px-4 py-8 max-w-4xl">
        <div class="text-center mb-8">
            <h1 class="text-3xl font-bold text-widdx-500 mb-2">🧪 WIDDX AI - Test Fixed</h1>
            <p class="text-gray-600 dark:text-gray-400">Testing the fixed version</p>
        </div>

        <!-- Chat Container -->
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow-lg overflow-hidden">
            <!-- Messages Area -->
            <div class="h-96 overflow-y-auto p-6 space-y-4" id="messages-container">
                <div class="text-center text-gray-500 dark:text-gray-400">
                    <p>👋 Welcome! Send a message to test WIDDX AI</p>
                    <p class="text-sm mt-2">Try: "مرحبا" or "hello"</p>
                </div>
            </div>

            <!-- Thinking Indicator -->
            <div id="thinking-indicator" class="hidden px-6 py-2 border-t border-gray-200 dark:border-gray-700">
                <div class="flex items-center space-x-2 text-gray-500 dark:text-gray-400">
                    <div class="flex space-x-1">
                        <div class="w-2 h-2 bg-gray-400 rounded-full animate-bounce"></div>
                        <div class="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style="animation-delay: 0.1s"></div>
                        <div class="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style="animation-delay: 0.2s"></div>
                    </div>
                    <span class="text-sm">WIDDX is thinking...</span>
                </div>
            </div>

            <!-- Input Area -->
            <div class="border-t border-gray-200 dark:border-gray-700 p-4">
                <div class="flex space-x-4">
                    <textarea 
                        id="message-input" 
                        placeholder="Type your message here... (مرحبا or hello)"
                        class="flex-1 resize-none border border-gray-300 dark:border-gray-600 rounded-lg px-4 py-2 bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-widdx-500"
                        rows="1"
                    ></textarea>
                    <button 
                        id="send-button"
                        class="px-6 py-2 bg-widdx-500 text-white rounded-lg hover:bg-widdx-600 focus:outline-none focus:ring-2 focus:ring-widdx-500 disabled:opacity-50 disabled:cursor-not-allowed"
                        disabled
                    >
                        Send
                    </button>
                </div>
                <div class="mt-2 text-xs text-gray-500 dark:text-gray-400">
                    Press Enter to send • Shift+Enter for new line
                </div>
            </div>
        </div>

        <!-- Debug Info -->
        <div class="mt-6 bg-gray-100 dark:bg-gray-800 rounded-lg p-4">
            <h3 class="font-semibold mb-2">🔍 Debug Info</h3>
            <div class="text-sm space-y-1">
                <div>Status: <span id="status">Loading...</span></div>
                <div>API: <span id="api-status">Checking...</span></div>
                <div>Last Response: <span id="last-response">None</span></div>
            </div>
        </div>
    </div>

    <script src="{{ asset('js/widdx-fixed.js') }}?v={{ time() }}"></script>
    
    <script>
        // Update send button state
        const messageInput = document.getElementById('message-input');
        const sendButton = document.getElementById('send-button');
        
        messageInput.addEventListener('input', () => {
            const hasText = messageInput.value.trim().length > 0;
            sendButton.disabled = !hasText;
        });

        // Test API on load
        window.addEventListener('load', async () => {
            document.getElementById('status').textContent = 'Loaded';
            
            // Test API
            try {
                const response = await fetch('/api/chat/health');
                const data = await response.json();
                document.getElementById('api-status').textContent = data.status === 'ok' ? 'Working ✅' : 'Error ❌';
            } catch (error) {
                document.getElementById('api-status').textContent = 'Error ❌';
            }
        });

        // Monitor responses
        const originalAddMessage = window.widdxFixed?.addMessage;
        if (originalAddMessage) {
            window.widdxFixed.addMessage = function(content, sender, metadata) {
                if (sender === 'assistant') {
                    document.getElementById('last-response').textContent = content.substring(0, 50) + '...';
                }
                return originalAddMessage.call(this, content, sender, metadata);
            };
        }
    </script>
</body>
</html>
