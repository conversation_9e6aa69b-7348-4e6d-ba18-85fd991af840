/**
 * Fix Test - Test the specific issues mentioned by user
 */

console.log('🔧 Testing specific fixes...');

function testMessageSending() {
    console.log('📤 Testing message sending...');
    
    const messageInput = document.getElementById('message-input');
    const sendButton = document.getElementById('send-button');
    
    if (!messageInput || !sendButton) {
        console.error('❌ Message input or send button not found');
        return false;
    }
    
    // Test 1: Type a message
    messageInput.value = 'مرحبا';
    messageInput.dispatchEvent(new Event('input'));
    
    // Check if send button is enabled
    if (sendButton.disabled) {
        console.error('❌ Send button should be enabled with text');
        return false;
    }
    
    console.log('✅ Send button enabled with text');
    
    // Test 2: Try to send message
    try {
        if (window.widdxModern && typeof window.widdxModern.submitMessage === 'function') {
            console.log('✅ submitMessage function available');
            return true;
        } else {
            console.error('❌ submitMessage function not available');
            return false;
        }
    } catch (error) {
        console.error('❌ Error testing message sending:', error);
        return false;
    }
}

function testThemeToggle() {
    console.log('🌙 Testing theme toggle...');
    
    const themeToggle = document.getElementById('theme-toggle');
    if (!themeToggle) {
        console.error('❌ Theme toggle button not found');
        return false;
    }
    
    // Get initial theme
    const initialDark = document.documentElement.classList.contains('dark');
    console.log(`Initial theme: ${initialDark ? 'dark' : 'light'}`);
    
    // Try to toggle
    try {
        themeToggle.click();
        
        // Check if theme changed
        setTimeout(() => {
            const newDark = document.documentElement.classList.contains('dark');
            if (newDark !== initialDark) {
                console.log('✅ Theme toggle working');
                // Toggle back
                themeToggle.click();
            } else {
                console.error('❌ Theme did not change');
            }
        }, 100);
        
        return true;
    } catch (error) {
        console.error('❌ Error testing theme toggle:', error);
        return false;
    }
}

function testSettingsModal() {
    console.log('⚙️ Testing settings modal...');
    
    const settingsToggle = document.getElementById('settings-toggle');
    const settingsModal = document.getElementById('settings-modal');
    
    if (!settingsToggle) {
        console.error('❌ Settings toggle button not found');
        return false;
    }
    
    if (!settingsModal) {
        console.error('❌ Settings modal not found');
        return false;
    }
    
    try {
        // Try to open settings
        settingsToggle.click();
        
        setTimeout(() => {
            if (!settingsModal.classList.contains('hidden')) {
                console.log('✅ Settings modal opened');
                
                // Try to close it
                const closeButton = document.getElementById('close-settings-modal');
                if (closeButton) {
                    closeButton.click();
                    setTimeout(() => {
                        if (settingsModal.classList.contains('hidden')) {
                            console.log('✅ Settings modal closed');
                        } else {
                            console.error('❌ Settings modal did not close');
                        }
                    }, 100);
                }
            } else {
                console.error('❌ Settings modal did not open');
            }
        }, 100);
        
        return true;
    } catch (error) {
        console.error('❌ Error testing settings modal:', error);
        return false;
    }
}

function testEnterKey() {
    console.log('⌨️ Testing Enter key...');
    
    const messageInput = document.getElementById('message-input');
    if (!messageInput) {
        console.error('❌ Message input not found');
        return false;
    }
    
    // Set a test message
    messageInput.value = 'Test Enter key';
    messageInput.focus();
    
    // Create Enter key event
    const enterEvent = new KeyboardEvent('keydown', {
        key: 'Enter',
        bubbles: true,
        cancelable: true
    });
    
    try {
        // Dispatch the event
        const result = messageInput.dispatchEvent(enterEvent);
        console.log('✅ Enter key event dispatched');
        
        // Check if message was cleared (indicating it was sent)
        setTimeout(() => {
            if (messageInput.value === '') {
                console.log('✅ Enter key triggered message sending');
            } else {
                console.log('ℹ️ Enter key event handled (message still in input)');
            }
        }, 100);
        
        return true;
    } catch (error) {
        console.error('❌ Error testing Enter key:', error);
        return false;
    }
}

function testAPI() {
    console.log('🌐 Testing API...');
    
    return fetch('/api/chat/health')
        .then(response => {
            if (response.ok) {
                console.log('✅ API health check passed');
                return true;
            } else {
                console.error('❌ API health check failed:', response.status);
                return false;
            }
        })
        .catch(error => {
            console.error('❌ API health check error:', error);
            return false;
        });
}

async function runFixTests() {
    console.log('🚀 Running Fix Tests...');
    console.log('========================');
    
    const results = {
        messageSending: testMessageSending(),
        themeToggle: testThemeToggle(),
        settingsModal: testSettingsModal(),
        enterKey: testEnterKey(),
        api: await testAPI()
    };
    
    console.log('\n📊 FIX TEST RESULTS:');
    console.log('====================');
    
    let passed = 0;
    Object.entries(results).forEach(([test, result]) => {
        const status = result ? '✅ PASS' : '❌ FAIL';
        console.log(`${status} ${test}`);
        if (result) passed++;
    });
    
    const total = Object.keys(results).length;
    console.log(`\n🎯 Overall: ${passed}/${total} tests passed (${((passed/total)*100).toFixed(1)}%)`);
    
    if (passed === total) {
        console.log('🎉 All fixes are working correctly!');
        if (window.widdxModern && typeof window.widdxModern.showNotification === 'function') {
            window.widdxModern.showNotification('All fixes working! 🎉', 'success');
        }
    } else {
        console.log('⚠️ Some fixes need attention. Check the console for details.');
        if (window.widdxModern && typeof window.widdxModern.showNotification === 'function') {
            window.widdxModern.showNotification(`${passed}/${total} fixes working`, 'warning');
        }
    }
    
    return results;
}

// Auto-run tests after a delay
setTimeout(() => {
    runFixTests();
}, 3000);

// Export for manual testing
window.runFixTests = runFixTests;
