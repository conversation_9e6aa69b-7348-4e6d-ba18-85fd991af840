<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;

class SimpleChatController extends Controller
{
    /**
     * Handle simple chat requests for testing
     */
    public function chat(Request $request): JsonResponse
    {
        try {
            // Get basic input
            $message = $request->input('message', '');
            $features = $request->input('features', []);
            $language = $request->input('language', 'en');
            $sessionId = $request->input('session_id', 'session_' . time());

            // Basic validation
            if (empty(trim($message))) {
                return response()->json([
                    'success' => false,
                    'error' => 'Message is required',
                    'message' => 'Please enter a message.'
                ], 400);
            }

            // Generate simple response based on message
            $response = $this->generateSimpleResponse($message, $features, $language);

            return response()->json([
                'success' => true,
                'message' => $response,
                'session_id' => $sessionId,
                'language' => $language,
                'features_used' => array_keys(array_filter($features)),
                'timestamp' => date('c'),
                'processing_time' => 0.5
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'error' => 'Internal server error',
                'message' => 'I apologize, but I encountered an error processing your request. Please try again.',
                'debug' => config('app.debug') ? $e->getMessage() : null
            ], 500);
        }
    }

    /**
     * Generate a simple response based on the input
     */
    private function generateSimpleResponse(string $message, array $features, string $language): string
    {
        $message = strtolower(trim($message));

        // Detect Arabic text
        $isArabic = $language === 'ar' || preg_match('/[\x{0600}-\x{06FF}]/u', $message);

        if ($isArabic) {
            // Arabic responses
            if (strpos($message, 'مرحبا') !== false || strpos($message, 'السلام') !== false || strpos($message, 'أهلا') !== false) {
                return 'مرحباً بك! أنا WIDDX AI، مساعدك الذكي. كيف يمكنني مساعدتك اليوم؟';
            }

            if (strpos($message, 'كيف حالك') !== false || strpos($message, 'كيفك') !== false) {
                return 'أنا بخير، شكراً لسؤالك! أنا هنا لمساعدتك في أي شيء تحتاجه.';
            }

            if (strpos($message, 'ما اسمك') !== false || strpos($message, 'من أنت') !== false) {
                return 'أنا WIDDX AI، مساعد ذكي متقدم. يمكنني مساعدتك في البحث، توليد الصور، البرمجة، والكثير من المهام الأخرى.';
            }

            if (strpos($message, 'صورة') !== false || strpos($message, 'رسم') !== false) {
                return 'يمكنني مساعدتك في توليد الصور! فعّل ميزة "توليد الصور" من الشريط الجانبي ثم اطلب مني رسم أي شيء تريده.';
            }

            if (strpos($message, 'بحث') !== false || strpos($message, 'ابحث') !== false) {
                return 'يمكنني البحث في الإنترنت لك! فعّل ميزة "البحث" أو "البحث العميق" من الشريط الجانبي وسأجد لك المعلومات التي تحتاجها.';
            }

            return 'شكراً لك على رسالتك! أنا WIDDX AI وأنا هنا لمساعدتك. يمكنك تفعيل الميزات المختلفة من الشريط الجانبي للحصول على إمكانيات متقدمة مثل البحث وتوليد الصور.';
        } else {
            // English responses
            if (strpos($message, 'hello') !== false || strpos($message, 'hi') !== false || strpos($message, 'hey') !== false) {
                return 'Hello! I\'m WIDDX AI, your intelligent assistant. How can I help you today?';
            }

            if (strpos($message, 'how are you') !== false || strpos($message, 'how do you do') !== false) {
                return 'I\'m doing great, thank you for asking! I\'m here to help you with whatever you need.';
            }

            if (strpos($message, 'what is your name') !== false || strpos($message, 'who are you') !== false) {
                return 'I\'m WIDDX AI, an advanced intelligent assistant. I can help you with search, image generation, coding, and many other tasks.';
            }

            if (strpos($message, 'image') !== false || strpos($message, 'picture') !== false || strpos($message, 'draw') !== false) {
                return 'I can help you generate images! Enable the "Image Generation" feature from the sidebar and then ask me to draw anything you want.';
            }

            if (strpos($message, 'search') !== false || strpos($message, 'find') !== false) {
                return 'I can search the internet for you! Enable the "Search" or "Deep Search" feature from the sidebar and I\'ll find the information you need.';
            }

            if (strpos($message, 'code') !== false || strpos($message, 'program') !== false) {
                return 'I can help you with coding! I can write code, debug issues, explain concepts, and assist with various programming languages.';
            }

            // Check active features
            $activeFeatures = array_keys(array_filter($features));
            if (!empty($activeFeatures)) {
                $featuresText = implode(', ', $activeFeatures);
                return "Thank you for your message! I see you have these features active: {$featuresText}. I'm WIDDX AI and I'm ready to help you with advanced capabilities.";
            }

            return 'Thank you for your message! I\'m WIDDX AI and I\'m here to help you. You can enable different features from the sidebar to get advanced capabilities like search and image generation.';
        }
    }

    /**
     * Health check endpoint
     */
    public function health(): JsonResponse
    {
        return response()->json([
            'status' => 'ok',
            'service' => 'WIDDX Simple Chat',
            'timestamp' => date('c'),
            'version' => '1.0.0'
        ]);
    }

    /**
     * Get available features
     */
    public function features(): JsonResponse
    {
        return response()->json([
            'available_features' => [
                'search' => [
                    'name' => 'Web Search',
                    'description' => 'Search the internet for information',
                    'enabled' => true
                ],
                'deepSearch' => [
                    'name' => 'Deep Search',
                    'description' => 'Advanced search with detailed analysis',
                    'enabled' => true
                ],
                'thinkMode' => [
                    'name' => 'Think Mode',
                    'description' => 'Show detailed thinking process',
                    'enabled' => true
                ],
                'imageGeneration' => [
                    'name' => 'Image Generation',
                    'description' => 'Generate images from text descriptions',
                    'enabled' => true
                ],
                'vision' => [
                    'name' => 'Vision Analysis',
                    'description' => 'Analyze and understand images',
                    'enabled' => true
                ]
            ],
            'languages' => ['en', 'ar', 'es', 'fr', 'de', 'zh', 'ja', 'ko', 'ru'],
            'max_message_length' => 4000
        ]);
    }
}
