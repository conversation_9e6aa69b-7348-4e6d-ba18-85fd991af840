<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;

class SimpleChatController extends Controller
{
    /**
     * Handle simple chat requests for testing
     */
    public function chat(Request $request): JsonResponse
    {
        try {
            // Get basic input
            $message = trim($request->input('message', ''));
            $features = $request->input('features', []);
            $language = $request->input('language', 'auto');
            $sessionId = $request->input('session_id', 'session_' . time() . '_' . rand(1000, 9999));

            // Auto-detect language if not specified
            if ($language === 'auto' || empty($language)) {
                $language = preg_match('/[\x{0600}-\x{06FF}]/u', $message) ? 'ar' : 'en';
            }

            // Basic validation
            if (empty(trim($message))) {
                return response()->json([
                    'success' => false,
                    'error' => 'Message is required',
                    'message' => 'Please enter a message.'
                ], 400);
            }

            // Generate simple response based on message
            $response = $this->generateSimpleResponse($message, $features, $language);

            return response()->json([
                'success' => true,
                'message' => $response,
                'session_id' => $sessionId,
                'language' => $language,
                'features_used' => array_keys(array_filter($features)),
                'timestamp' => date('c'),
                'processing_time' => 0.5
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'error' => 'Internal server error',
                'message' => 'I apologize, but I encountered an error processing your request. Please try again.',
                'debug' => config('app.debug') ? $e->getMessage() : null
            ], 500);
        }
    }

    /**
     * Generate an intelligent response based on the input
     */
    private function generateSimpleResponse(string $message, array $features, string $language): string
    {
        $originalMessage = $message;
        $message = strtolower(trim($message));

        // Detect Arabic text
        $isArabic = $language === 'ar' || preg_match('/[\x{0600}-\x{06FF}]/u', $originalMessage);

        if ($isArabic) {
            // Arabic responses with more variety
            if (strpos($message, 'مرحبا') !== false || strpos($message, 'السلام') !== false ||
                strpos($message, 'أهلا') !== false || strpos($message, 'هلا') !== false) {
                $greetings = [
                    'مرحباً بك! أنا WIDDX AI، مساعدك الذكي المتقدم. كيف يمكنني مساعدتك اليوم؟',
                    'أهلاً وسهلاً! أنا WIDDX AI، هنا لمساعدتك في أي شيء تحتاجه.',
                    'مرحباً! أنا WIDDX AI، مساعدك الذكي. أخبرني كيف يمكنني خدمتك؟'
                ];
                return $greetings[array_rand($greetings)];
            }

            if (strpos($message, 'كيف حالك') !== false || strpos($message, 'كيفك') !== false ||
                strpos($message, 'شلونك') !== false) {
                return 'أنا بخير جداً، شكراً لسؤالك! أنا هنا ومستعد لمساعدتك في أي شيء تحتاجه.';
            }

            if (strpos($message, 'ما اسمك') !== false || strpos($message, 'من أنت') !== false ||
                strpos($message, 'شنو اسمك') !== false) {
                return 'أنا WIDDX AI، مساعد ذكي متطور ومتقدم. أستطيع مساعدتك في البحث، توليد الصور، البرمجة، التحليل، والكثير من المهام الأخرى.';
            }

            if (strpos($message, 'صورة') !== false || strpos($message, 'رسم') !== false ||
                strpos($message, 'ارسم') !== false) {
                return 'يمكنني مساعدتك في توليد صور عالية الجودة! فعّل ميزة "توليد الصور" من الشريط الجانبي وسأرسم لك أي شيء تريده بدقة وإبداع.';
            }

            if (strpos($message, 'بحث') !== false || strpos($message, 'ابحث') !== false ||
                strpos($message, 'دور') !== false) {
                return 'أستطيع البحث في الإنترنت لك والعثور على أحدث المعلومات! فعّل ميزة "البحث" أو "البحث العميق" وسأجد لك ما تحتاجه.';
            }

            if (strpos($message, 'شكرا') !== false || strpos($message, 'شكراً') !== false ||
                strpos($message, 'مشكور') !== false) {
                return 'العفو! أنا سعيد جداً لمساعدتك. لا تتردد في سؤالي عن أي شيء آخر.';
            }

            if (strpos($message, 'برمجة') !== false || strpos($message, 'كود') !== false ||
                strpos($message, 'برنامج') !== false) {
                return 'يمكنني مساعدتك في البرمجة! أستطيع كتابة الكود، إصلاح الأخطاء، شرح المفاهيم، ومساعدتك في مختلف لغات البرمجة.';
            }

            return 'شكراً لك على رسالتك! أنا WIDDX AI وأنا هنا لمساعدتك. يمكنك تفعيل الميزات المختلفة من الشريط الجانبي للحصول على إمكانيات متقدمة مثل البحث وتوليد الصور.';

        } else {
            // English responses with more variety
            if (strpos($message, 'hello') !== false || strpos($message, 'hi') !== false ||
                strpos($message, 'hey') !== false || strpos($message, 'greetings') !== false) {
                $greetings = [
                    'Hello! I\'m WIDDX AI, your advanced intelligent assistant. How can I help you today?',
                    'Hi there! I\'m WIDDX AI, here to assist you with whatever you need.',
                    'Greetings! I\'m WIDDX AI, your smart assistant. What can I do for you?'
                ];
                return $greetings[array_rand($greetings)];
            }

            if (strpos($message, 'how are you') !== false || strpos($message, 'how do you do') !== false ||
                strpos($message, 'what\'s up') !== false) {
                return 'I\'m doing excellent, thank you for asking! I\'m here and ready to help you with whatever you need.';
            }

            if (strpos($message, 'what is your name') !== false || strpos($message, 'who are you') !== false ||
                strpos($message, 'what are you') !== false) {
                return 'I\'m WIDDX AI, an advanced intelligent assistant. I can help you with search, image generation, coding, analysis, and many other tasks.';
            }

            if (strpos($message, 'image') !== false || strpos($message, 'picture') !== false ||
                strpos($message, 'draw') !== false || strpos($message, 'generate') !== false) {
                return 'I can help you generate high-quality images! Enable the "Image Generation" feature from the sidebar and ask me to create anything you want with precision and creativity.';
            }

            if (strpos($message, 'search') !== false || strpos($message, 'find') !== false ||
                strpos($message, 'look up') !== false) {
                return 'I can search the internet for you and find the latest information! Enable the "Search" or "Deep Search" feature from the sidebar and I\'ll find what you need.';
            }

            if (strpos($message, 'code') !== false || strpos($message, 'program') !== false ||
                strpos($message, 'develop') !== false) {
                return 'I can help you with coding! I can write code, debug issues, explain concepts, and assist with various programming languages and frameworks.';
            }

            if (strpos($message, 'thank') !== false || strpos($message, 'thanks') !== false ||
                strpos($message, 'appreciate') !== false) {
                return 'You\'re very welcome! I\'m happy to help. Feel free to ask me anything else you need assistance with.';
            }

            // Check active features
            $activeFeatures = array_keys(array_filter($features));
            if (!empty($activeFeatures)) {
                $featuresText = implode(', ', $activeFeatures);
                return "Thank you for your message! I see you have these features active: {$featuresText}. I'm WIDDX AI and I'm ready to help you with advanced capabilities.";
            }

            return 'Thank you for your message! I\'m WIDDX AI and I\'m here to help you. You can enable different features from the sidebar to get advanced capabilities like search and image generation.';
        }
    }

    /**
     * Health check endpoint
     */
    public function health(): JsonResponse
    {
        return response()->json([
            'status' => 'ok',
            'service' => 'WIDDX Simple Chat',
            'timestamp' => date('c'),
            'version' => '1.0.0'
        ]);
    }

    /**
     * Get available features
     */
    public function features(): JsonResponse
    {
        return response()->json([
            'available_features' => [
                'search' => [
                    'name' => 'Web Search',
                    'description' => 'Search the internet for information',
                    'enabled' => true
                ],
                'deepSearch' => [
                    'name' => 'Deep Search',
                    'description' => 'Advanced search with detailed analysis',
                    'enabled' => true
                ],
                'thinkMode' => [
                    'name' => 'Think Mode',
                    'description' => 'Show detailed thinking process',
                    'enabled' => true
                ],
                'imageGeneration' => [
                    'name' => 'Image Generation',
                    'description' => 'Generate images from text descriptions',
                    'enabled' => true
                ],
                'vision' => [
                    'name' => 'Vision Analysis',
                    'description' => 'Analyze and understand images',
                    'enabled' => true
                ]
            ],
            'languages' => ['en', 'ar', 'es', 'fr', 'de', 'zh', 'ja', 'ko', 'ru'],
            'max_message_length' => 4000
        ]);
    }
}
