<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Validator;

class SimpleChatController extends Controller
{
    /**
     * Handle simple chat requests for testing
     */
    public function chat(Request $request): JsonResponse
    {
        try {
            // Validate request
            $validator = Validator::make($request->all(), [
                'message' => 'required|string|max:4000',
                'features' => 'sometimes|array',
                'language' => 'sometimes|string|max:10',
                'session_id' => 'sometimes|string|max:255'
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'success' => false,
                    'error' => 'Validation failed',
                    'message' => 'Please check your input and try again.',
                    'details' => $validator->errors()
                ], 422);
            }

            $message = $request->input('message');
            $features = $request->input('features', []);
            $language = $request->input('language', 'en');
            $sessionId = $request->input('session_id', 'session_' . time());

            Log::info('Simple chat request received', [
                'message' => substr($message, 0, 100),
                'features' => $features,
                'language' => $language,
                'session_id' => $sessionId
            ]);

            // Generate simple response based on message
            $response = $this->generateSimpleResponse($message, $features, $language);

            return response()->json([
                'success' => true,
                'message' => $response,
                'session_id' => $sessionId,
                'language' => $language,
                'features_used' => array_keys(array_filter($features)),
                'timestamp' => now()->toISOString(),
                'processing_time' => 0.5
            ]);

        } catch (\Exception $e) {
            Log::error('Simple chat error', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return response()->json([
                'success' => false,
                'error' => 'Internal server error',
                'message' => 'I apologize, but I encountered an error processing your request. Please try again.',
                'debug' => config('app.debug') ? $e->getMessage() : null
            ], 500);
        }
    }

    /**
     * Generate a simple response based on the input
     */
    private function generateSimpleResponse(string $message, array $features, string $language): string
    {
        $message = strtolower(trim($message));
        
        // Arabic responses
        if ($language === 'ar') {
            if (str_contains($message, 'مرحبا') || str_contains($message, 'السلام') || str_contains($message, 'أهلا')) {
                return 'مرحباً بك! أنا WIDDX AI، مساعدك الذكي. كيف يمكنني مساعدتك اليوم؟';
            }
            
            if (str_contains($message, 'كيف حالك') || str_contains($message, 'كيفك')) {
                return 'أنا بخير، شكراً لسؤالك! أنا هنا لمساعدتك في أي شيء تحتاجه.';
            }
            
            if (str_contains($message, 'ما اسمك') || str_contains($message, 'من أنت')) {
                return 'أنا WIDDX AI، مساعد ذكي متقدم. يمكنني مساعدتك في البحث، توليد الصور، البرمجة، والكثير من المهام الأخرى.';
            }
            
            if (str_contains($message, 'صورة') || str_contains($message, 'رسم')) {
                return 'يمكنني مساعدتك في توليد الصور! فعّل ميزة "توليد الصور" من الشريط الجانبي ثم اطلب مني رسم أي شيء تريده.';
            }
            
            if (str_contains($message, 'بحث') || str_contains($message, 'ابحث')) {
                return 'يمكنني البحث في الإنترنت لك! فعّل ميزة "البحث" أو "البحث العميق" من الشريط الجانبي وسأجد لك المعلومات التي تحتاجها.';
            }
            
            return 'شكراً لك على رسالتك! أنا WIDDX AI وأنا هنا لمساعدتك. يمكنك تفعيل الميزات المختلفة من الشريط الجانبي للحصول على إمكانيات متقدمة مثل البحث وتوليد الصور.';
        }
        
        // English responses
        if (str_contains($message, 'hello') || str_contains($message, 'hi') || str_contains($message, 'hey')) {
            return 'Hello! I\'m WIDDX AI, your intelligent assistant. How can I help you today?';
        }
        
        if (str_contains($message, 'how are you') || str_contains($message, 'how do you do')) {
            return 'I\'m doing great, thank you for asking! I\'m here to help you with whatever you need.';
        }
        
        if (str_contains($message, 'what is your name') || str_contains($message, 'who are you')) {
            return 'I\'m WIDDX AI, an advanced intelligent assistant. I can help you with search, image generation, coding, and many other tasks.';
        }
        
        if (str_contains($message, 'image') || str_contains($message, 'picture') || str_contains($message, 'draw')) {
            return 'I can help you generate images! Enable the "Image Generation" feature from the sidebar and then ask me to draw anything you want.';
        }
        
        if (str_contains($message, 'search') || str_contains($message, 'find')) {
            return 'I can search the internet for you! Enable the "Search" or "Deep Search" feature from the sidebar and I\'ll find the information you need.';
        }
        
        if (str_contains($message, 'code') || str_contains($message, 'program')) {
            return 'I can help you with coding! I can write code, debug issues, explain concepts, and assist with various programming languages.';
        }
        
        // Check active features
        $activeFeatures = array_keys(array_filter($features));
        if (!empty($activeFeatures)) {
            $featuresText = implode(', ', $activeFeatures);
            return "Thank you for your message! I see you have these features active: {$featuresText}. I'm WIDDX AI and I'm ready to help you with advanced capabilities.";
        }
        
        return 'Thank you for your message! I\'m WIDDX AI and I\'m here to help you. You can enable different features from the sidebar to get advanced capabilities like search and image generation.';
    }

    /**
     * Health check endpoint
     */
    public function health(): JsonResponse
    {
        return response()->json([
            'status' => 'ok',
            'service' => 'WIDDX Simple Chat',
            'timestamp' => now()->toISOString(),
            'version' => '1.0.0'
        ]);
    }

    /**
     * Get available features
     */
    public function features(): JsonResponse
    {
        return response()->json([
            'available_features' => [
                'search' => [
                    'name' => 'Web Search',
                    'description' => 'Search the internet for information',
                    'enabled' => true
                ],
                'deepSearch' => [
                    'name' => 'Deep Search',
                    'description' => 'Advanced search with detailed analysis',
                    'enabled' => true
                ],
                'thinkMode' => [
                    'name' => 'Think Mode',
                    'description' => 'Show detailed thinking process',
                    'enabled' => true
                ],
                'imageGeneration' => [
                    'name' => 'Image Generation',
                    'description' => 'Generate images from text descriptions',
                    'enabled' => true
                ],
                'vision' => [
                    'name' => 'Vision Analysis',
                    'description' => 'Analyze and understand images',
                    'enabled' => true
                ]
            ],
            'languages' => ['en', 'ar', 'es', 'fr', 'de', 'zh', 'ja', 'ko', 'ru'],
            'max_message_length' => 4000
        ]);
    }
}
