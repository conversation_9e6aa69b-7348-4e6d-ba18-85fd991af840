<!DOCTYPE html>
<html lang="en" class="dark">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>WIDDX AI - Help & Documentation</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script>
        tailwind.config = {
            darkMode: 'class',
            theme: {
                extend: {
                    colors: {
                        'widdx': {
                            '50': '#eff6ff',
                            '500': '#3B82F6',
                            '600': '#2563EB',
                            '900': '#1e3a8a'
                        }
                    }
                }
            }
        }
    </script>
</head>
<body class="bg-gray-50 dark:bg-gray-900 text-gray-900 dark:text-white min-h-screen">
    <!-- Header -->
    <header class="bg-white dark:bg-gray-800 shadow-sm border-b border-gray-200 dark:border-gray-700">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between items-center h-16">
                <div class="flex items-center">
                    <a href="/" class="flex items-center space-x-3">
                        <div class="w-8 h-8 bg-gradient-to-br from-widdx-500 to-widdx-600 rounded-lg flex items-center justify-center">
                            <span class="text-white font-bold text-sm">W</span>
                        </div>
                        <span class="text-xl font-bold">WIDDX AI</span>
                    </a>
                </div>
                <div class="flex items-center space-x-4">
                    <a href="/" class="text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white">
                        <i class="fas fa-arrow-left mr-2"></i>Back to Chat
                    </a>
                    <button id="theme-toggle" class="p-2 text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200">
                        <i class="fas fa-moon dark:hidden"></i>
                        <i class="hidden fas fa-sun dark:inline"></i>
                    </button>
                </div>
            </div>
        </div>
    </header>

    <!-- Main Content -->
    <main class="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div class="mb-8">
            <h1 class="text-3xl font-bold text-gray-900 dark:text-white">Help & Documentation</h1>
            <p class="mt-2 text-gray-600 dark:text-gray-400">Learn how to use WIDDX AI effectively</p>
        </div>

        <div class="grid grid-cols-1 lg:grid-cols-4 gap-8">
            <!-- Navigation -->
            <div class="lg:col-span-1">
                <nav class="space-y-2 sticky top-8">
                    <a href="#getting-started" class="help-nav-item active block px-4 py-2 text-sm font-medium rounded-lg bg-widdx-500 text-white">
                        Getting Started
                    </a>
                    <a href="#features" class="help-nav-item block px-4 py-2 text-sm font-medium rounded-lg text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700">
                        Features
                    </a>
                    <a href="#auto-features" class="help-nav-item block px-4 py-2 text-sm font-medium rounded-lg text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700">
                        Auto Features
                    </a>
                    <a href="#manual-features" class="help-nav-item block px-4 py-2 text-sm font-medium rounded-lg text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700">
                        Manual Features
                    </a>
                    <a href="#tips" class="help-nav-item block px-4 py-2 text-sm font-medium rounded-lg text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700">
                        Tips & Tricks
                    </a>
                    <a href="#troubleshooting" class="help-nav-item block px-4 py-2 text-sm font-medium rounded-lg text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700">
                        Troubleshooting
                    </a>
                </nav>
            </div>

            <!-- Content -->
            <div class="lg:col-span-3">
                <!-- Getting Started -->
                <div id="getting-started" class="help-section">
                    <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6 mb-6">
                        <h2 class="text-2xl font-semibold mb-4">Getting Started</h2>
                        
                        <div class="space-y-4">
                            <div>
                                <h3 class="text-lg font-medium mb-2">Welcome to WIDDX AI</h3>
                                <p class="text-gray-600 dark:text-gray-400">WIDDX AI is an advanced intelligent assistant that can help you with various tasks including conversation, image generation, translation, programming, and much more.</p>
                            </div>

                            <div>
                                <h3 class="text-lg font-medium mb-2">How to Start</h3>
                                <ol class="list-decimal list-inside space-y-2 text-gray-600 dark:text-gray-400">
                                    <li>Simply type your message in the chat input at the bottom</li>
                                    <li>Press Enter or click the Send button</li>
                                    <li>WIDDX will automatically detect what you need and respond accordingly</li>
                                    <li>Use the sidebar features for advanced capabilities</li>
                                </ol>
                            </div>

                            <div class="bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg p-4">
                                <div class="flex items-start">
                                    <i class="fas fa-info-circle text-blue-500 mt-1 mr-3"></i>
                                    <div>
                                        <h4 class="font-medium text-blue-900 dark:text-blue-100">Pro Tip</h4>
                                        <p class="text-blue-700 dark:text-blue-300 text-sm">WIDDX automatically activates features based on your requests. For example, asking to "draw a cat" will automatically enable image generation!</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Features -->
                <div id="features" class="help-section hidden">
                    <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6 mb-6">
                        <h2 class="text-2xl font-semibold mb-4">Features Overview</h2>
                        
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                            <div class="space-y-4">
                                <h3 class="text-lg font-medium">Automatic Features</h3>
                                <div class="space-y-3">
                                    <div class="flex items-start space-x-3">
                                        <i class="fas fa-search text-widdx-500 mt-1"></i>
                                        <div>
                                            <h4 class="font-medium">Web Search</h4>
                                            <p class="text-sm text-gray-600 dark:text-gray-400">Automatically searches for current information</p>
                                        </div>
                                    </div>
                                    <div class="flex items-start space-x-3">
                                        <i class="fas fa-image text-widdx-500 mt-1"></i>
                                        <div>
                                            <h4 class="font-medium">Image Generation</h4>
                                            <p class="text-sm text-gray-600 dark:text-gray-400">Creates images from text descriptions</p>
                                        </div>
                                    </div>
                                    <div class="flex items-start space-x-3">
                                        <i class="fas fa-language text-widdx-500 mt-1"></i>
                                        <div>
                                            <h4 class="font-medium">Translation</h4>
                                            <p class="text-sm text-gray-600 dark:text-gray-400">Translates text between languages</p>
                                        </div>
                                    </div>
                                    <div class="flex items-start space-x-3">
                                        <i class="fas fa-code text-widdx-500 mt-1"></i>
                                        <div>
                                            <h4 class="font-medium">Programming</h4>
                                            <p class="text-sm text-gray-600 dark:text-gray-400">Helps with coding and development</p>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="space-y-4">
                                <h3 class="text-lg font-medium">Manual Features</h3>
                                <div class="space-y-3">
                                    <div class="flex items-start space-x-3">
                                        <i class="fas fa-brain text-orange-500 mt-1"></i>
                                        <div>
                                            <h4 class="font-medium">Think Mode</h4>
                                            <p class="text-sm text-gray-600 dark:text-gray-400">Deep step-by-step reasoning</p>
                                        </div>
                                    </div>
                                    <div class="flex items-start space-x-3">
                                        <i class="fas fa-search-plus text-orange-500 mt-1"></i>
                                        <div>
                                            <h4 class="font-medium">Deep Search</h4>
                                            <p class="text-sm text-gray-600 dark:text-gray-400">Advanced comprehensive search</p>
                                        </div>
                                    </div>
                                    <div class="flex items-start space-x-3">
                                        <i class="fas fa-search-location text-orange-500 mt-1"></i>
                                        <div>
                                            <h4 class="font-medium">Ultra Deep Search</h4>
                                            <p class="text-sm text-gray-600 dark:text-gray-400">Academic-level research</p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Auto Features -->
                <div id="auto-features" class="help-section hidden">
                    <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6 mb-6">
                        <h2 class="text-2xl font-semibold mb-4">Automatic Features</h2>
                        
                        <div class="space-y-6">
                            <div>
                                <h3 class="text-lg font-medium mb-3">How Auto Features Work</h3>
                                <p class="text-gray-600 dark:text-gray-400 mb-4">WIDDX AI automatically detects what you need based on your message and activates the appropriate features without you having to manually enable them.</p>
                                
                                <div class="space-y-4">
                                    <div class="border border-gray-200 dark:border-gray-700 rounded-lg p-4">
                                        <h4 class="font-medium mb-2">🎨 Image Generation</h4>
                                        <p class="text-sm text-gray-600 dark:text-gray-400 mb-2">Triggered by keywords like:</p>
                                        <div class="flex flex-wrap gap-2">
                                            <span class="px-2 py-1 bg-gray-100 dark:bg-gray-700 rounded text-xs">"draw"</span>
                                            <span class="px-2 py-1 bg-gray-100 dark:bg-gray-700 rounded text-xs">"create image"</span>
                                            <span class="px-2 py-1 bg-gray-100 dark:bg-gray-700 rounded text-xs">"ارسم"</span>
                                            <span class="px-2 py-1 bg-gray-100 dark:bg-gray-700 rounded text-xs">"صورة"</span>
                                        </div>
                                    </div>

                                    <div class="border border-gray-200 dark:border-gray-700 rounded-lg p-4">
                                        <h4 class="font-medium mb-2">🌐 Translation</h4>
                                        <p class="text-sm text-gray-600 dark:text-gray-400 mb-2">Triggered by keywords like:</p>
                                        <div class="flex flex-wrap gap-2">
                                            <span class="px-2 py-1 bg-gray-100 dark:bg-gray-700 rounded text-xs">"translate"</span>
                                            <span class="px-2 py-1 bg-gray-100 dark:bg-gray-700 rounded text-xs">"ترجم"</span>
                                            <span class="px-2 py-1 bg-gray-100 dark:bg-gray-700 rounded text-xs">"convert to"</span>
                                        </div>
                                    </div>

                                    <div class="border border-gray-200 dark:border-gray-700 rounded-lg p-4">
                                        <h4 class="font-medium mb-2">🔍 Web Search</h4>
                                        <p class="text-sm text-gray-600 dark:text-gray-400 mb-2">Triggered by keywords like:</p>
                                        <div class="flex flex-wrap gap-2">
                                            <span class="px-2 py-1 bg-gray-100 dark:bg-gray-700 rounded text-xs">"latest"</span>
                                            <span class="px-2 py-1 bg-gray-700 rounded text-xs">"current"</span>
                                            <span class="px-2 py-1 bg-gray-100 dark:bg-gray-700 rounded text-xs">"weather"</span>
                                            <span class="px-2 py-1 bg-gray-100 dark:bg-gray-700 rounded text-xs">"ابحث"</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Manual Features -->
                <div id="manual-features" class="help-section hidden">
                    <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6 mb-6">
                        <h2 class="text-2xl font-semibold mb-4">Manual Features</h2>
                        
                        <div class="space-y-6">
                            <div>
                                <h3 class="text-lg font-medium mb-3">Advanced Features You Control</h3>
                                <p class="text-gray-600 dark:text-gray-400 mb-4">These features require manual activation from the sidebar and are designed for specialized tasks.</p>
                                
                                <div class="space-y-4">
                                    <div class="border border-gray-200 dark:border-gray-700 rounded-lg p-4">
                                        <h4 class="font-medium mb-2 flex items-center">
                                            <i class="fas fa-brain text-orange-500 mr-2"></i>
                                            Think Mode
                                        </h4>
                                        <p class="text-sm text-gray-600 dark:text-gray-400 mb-2">Enables deep, step-by-step reasoning for complex problems.</p>
                                        <p class="text-xs text-gray-500 dark:text-gray-500">Best for: Philosophy, complex analysis, problem-solving</p>
                                    </div>

                                    <div class="border border-gray-200 dark:border-gray-700 rounded-lg p-4">
                                        <h4 class="font-medium mb-2 flex items-center">
                                            <i class="fas fa-search-plus text-orange-500 mr-2"></i>
                                            Deep Search
                                        </h4>
                                        <p class="text-sm text-gray-600 dark:text-gray-400 mb-2">Advanced search with comprehensive analysis and multiple sources.</p>
                                        <p class="text-xs text-gray-500 dark:text-gray-500">Best for: Research, detailed information gathering</p>
                                    </div>

                                    <div class="border border-gray-200 dark:border-gray-700 rounded-lg p-4">
                                        <h4 class="font-medium mb-2 flex items-center">
                                            <i class="fas fa-search-location text-orange-500 mr-2"></i>
                                            Ultra Deep Search
                                        </h4>
                                        <p class="text-sm text-gray-600 dark:text-gray-400 mb-2">Academic-level research with extensive analysis and citations.</p>
                                        <p class="text-xs text-gray-500 dark:text-gray-500">Best for: Academic research, professional reports</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Tips -->
                <div id="tips" class="help-section hidden">
                    <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6 mb-6">
                        <h2 class="text-2xl font-semibold mb-4">Tips & Tricks</h2>
                        
                        <div class="space-y-4">
                            <div class="bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-lg p-4">
                                <h4 class="font-medium text-green-900 dark:text-green-100 mb-2">💡 Be Specific</h4>
                                <p class="text-green-700 dark:text-green-300 text-sm">The more specific your request, the better WIDDX can help. Instead of "help me code", try "write a Python function to sort a list".</p>
                            </div>

                            <div class="bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg p-4">
                                <h4 class="font-medium text-blue-900 dark:text-blue-100 mb-2">🌍 Use Any Language</h4>
                                <p class="text-blue-700 dark:text-blue-300 text-sm">WIDDX supports multiple languages. You can ask questions in Arabic, English, or other languages and get responses in the same language.</p>
                            </div>

                            <div class="bg-purple-50 dark:bg-purple-900/20 border border-purple-200 dark:border-purple-800 rounded-lg p-4">
                                <h4 class="font-medium text-purple-900 dark:text-purple-100 mb-2">🎯 Combine Features</h4>
                                <p class="text-purple-700 dark:text-purple-300 text-sm">You can enable manual features like Think Mode while asking for automatic features like image generation for more comprehensive responses.</p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Troubleshooting -->
                <div id="troubleshooting" class="help-section hidden">
                    <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6 mb-6">
                        <h2 class="text-2xl font-semibold mb-4">Troubleshooting</h2>
                        
                        <div class="space-y-4">
                            <div>
                                <h4 class="font-medium mb-2">WIDDX is taking too long to respond</h4>
                                <p class="text-sm text-gray-600 dark:text-gray-400">WIDDX uses advanced AI models which may take 15-30 seconds to generate high-quality responses. This is normal and ensures you get the best possible answer.</p>
                            </div>

                            <div>
                                <h4 class="font-medium mb-2">Features not working as expected</h4>
                                <p class="text-sm text-gray-600 dark:text-gray-400">Try refreshing the page or clearing your browser cache. Make sure you're using a modern browser with JavaScript enabled.</p>
                            </div>

                            <div>
                                <h4 class="font-medium mb-2">Auto features not activating</h4>
                                <p class="text-sm text-gray-600 dark:text-gray-400">Make sure your request contains clear keywords. For example, use "draw me a cat" instead of "I want a cat picture".</p>
                            </div>

                            <div>
                                <h4 class="font-medium mb-2">Settings not saving</h4>
                                <p class="text-sm text-gray-600 dark:text-gray-400">Check that your browser allows local storage. Some privacy modes or extensions might block this functionality.</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </main>

    <script>
        // Help navigation
        document.querySelectorAll('.help-nav-item').forEach(item => {
            item.addEventListener('click', (e) => {
                e.preventDefault();
                const target = e.target.getAttribute('href').substring(1);
                
                // Update navigation
                document.querySelectorAll('.help-nav-item').forEach(nav => {
                    nav.classList.remove('active', 'bg-widdx-500', 'text-white');
                    nav.classList.add('text-gray-700', 'dark:text-gray-300');
                });
                e.target.classList.add('active', 'bg-widdx-500', 'text-white');
                e.target.classList.remove('text-gray-700', 'dark:text-gray-300');
                
                // Update content
                document.querySelectorAll('.help-section').forEach(section => {
                    section.classList.add('hidden');
                });
                document.getElementById(target).classList.remove('hidden');
            });
        });

        // Theme toggle
        document.getElementById('theme-toggle').addEventListener('click', () => {
            const html = document.documentElement;
            const isDark = html.classList.contains('dark');
            
            if (isDark) {
                html.classList.remove('dark');
                localStorage.setItem('theme', 'light');
            } else {
                html.classList.add('dark');
                localStorage.setItem('theme', 'dark');
            }
        });

        // Load theme
        window.addEventListener('load', () => {
            const theme = localStorage.getItem('theme');
            if (theme === 'light') {
                document.documentElement.classList.remove('dark');
            } else {
                document.documentElement.classList.add('dark');
            }
        });
    </script>
</body>
</html>
<?php /**PATH C:\Users\<USER>\Desktop\widdx-ai\resources\views/help.blade.php ENDPATH**/ ?>