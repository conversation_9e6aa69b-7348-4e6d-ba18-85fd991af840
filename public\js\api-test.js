/**
 * WIDDX AI - API Connection Testing
 * اختبار الاتصال مع API
 */

class APITester {
    constructor() {
        this.baseUrl = window.location.origin;
        this.testResults = [];
        
        console.log('🔗 بدء اختبار الاتصال مع API...');
        this.runAPITests();
    }

    async log(message, type = 'info') {
        const timestamp = new Date().toLocaleTimeString();
        const emoji = type === 'success' ? '✅' : type === 'error' ? '❌' : type === 'warning' ? '⚠️' : 'ℹ️';
        console.log(`${emoji} [${timestamp}] ${message}`);
        
        this.testResults.push({
            timestamp,
            message,
            type
        });
    }

    async testEndpoint(name, url, method = 'GET', data = null) {
        try {
            this.log(`اختبار ${name}: ${method} ${url}`);
            
            const options = {
                method,
                headers: {
                    'Content-Type': 'application/json',
                    'Accept': 'application/json',
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]')?.getAttribute('content') || ''
                }
            };

            if (data && method !== 'GET') {
                options.body = JSON.stringify(data);
            }

            const response = await fetch(url, options);
            const responseData = await response.json();

            if (response.ok) {
                this.log(`✅ ${name}: نجح (${response.status})`, 'success');
                return { success: true, data: responseData, status: response.status };
            } else {
                this.log(`❌ ${name}: فشل (${response.status}) - ${responseData.message || 'خطأ غير معروف'}`, 'error');
                return { success: false, error: responseData, status: response.status };
            }
        } catch (error) {
            this.log(`❌ ${name}: خطأ في الشبكة - ${error.message}`, 'error');
            return { success: false, error: error.message };
        }
    }

    async runAPITests() {
        this.log('بدء اختبارات API...');

        // Test 1: Health Check
        await this.testEndpoint(
            'فحص الحالة',
            `${this.baseUrl}/api/health`
        );

        // Test 2: Chat Endpoint
        await this.testEndpoint(
            'نقطة الدردشة',
            `${this.baseUrl}/api/chat`,
            'POST',
            {
                message: 'مرحبا، هذا اختبار',
                features: {
                    search: false,
                    thinkMode: false,
                    imageGeneration: false
                },
                language: 'ar'
            }
        );

        // Test 3: Search Feature
        await this.testEndpoint(
            'ميزة البحث',
            `${this.baseUrl}/api/features/search`,
            'POST',
            {
                query: 'اختبار البحث',
                language: 'ar'
            }
        );

        // Test 4: Image Generation Test
        await this.testEndpoint(
            'اختبار توليد الصور',
            `${this.baseUrl}/api/features/test-image`
        );

        // Test 5: Think Mode
        await this.testEndpoint(
            'وضع التفكير',
            `${this.baseUrl}/api/features/think-mode`,
            'POST',
            {
                message: 'فكر في هذه المسألة',
                language: 'ar'
            }
        );

        // Test 6: Capabilities
        await this.testEndpoint(
            'القدرات المتاحة',
            `${this.baseUrl}/api/features/capabilities`
        );

        // Test 7: User Preferences
        await this.testEndpoint(
            'تفضيلات المستخدم',
            `${this.baseUrl}/api/preferences`
        );

        // Test 8: Feature Toggles
        await this.testEndpoint(
            'تبديل الميزات',
            `${this.baseUrl}/api/feature-toggles/available`
        );

        // Test 9: Unlimited Search
        await this.testEndpoint(
            'البحث غير المحدود',
            `${this.baseUrl}/api/unlimited-search`,
            'POST',
            {
                query: 'اختبار البحث غير المحدود',
                language: 'ar'
            }
        );

        // Test 10: DeepSeek Search
        await this.testEndpoint(
            'بحث DeepSeek',
            `${this.baseUrl}/api/deepseek-search/intelligent`,
            'POST',
            {
                query: 'اختبار البحث الذكي',
                language: 'ar'
            }
        );

        // Generate Report
        this.generateAPIReport();
    }

    generateAPIReport() {
        const successfulTests = this.testResults.filter(r => r.type === 'success').length;
        const failedTests = this.testResults.filter(r => r.type === 'error').length;
        const totalTests = successfulTests + failedTests;
        const successRate = totalTests > 0 ? ((successfulTests / totalTests) * 100).toFixed(1) : 0;

        console.log('\n' + '='.repeat(50));
        console.log('🔗 تقرير اختبار API');
        console.log('='.repeat(50));
        console.log(`📈 إجمالي الاختبارات: ${totalTests}`);
        console.log(`✅ نجح: ${successfulTests}`);
        console.log(`❌ فشل: ${failedTests}`);
        console.log(`📊 معدل النجاح: ${successRate}%`);
        console.log('='.repeat(50));

        if (failedTests === 0) {
            console.log('🎉 جميع اختبارات API نجحت!');
        } else if (successRate >= 70) {
            console.log('👍 معظم اختبارات API نجحت.');
        } else {
            console.log('⚠️ هناك مشاكل في الاتصال مع API.');
        }

        // Store results globally
        window.apiTestResults = {
            total: totalTests,
            passed: successfulTests,
            failed: failedTests,
            successRate: successRate,
            details: this.testResults
        };

        return {
            success: failedTests === 0,
            successRate: successRate,
            results: this.testResults
        };
    }
}

// Test specific API endpoint manually
window.testAPI = async function(endpoint, method = 'GET', data = null) {
    const tester = new APITester();
    return await tester.testEndpoint('Manual Test', endpoint, method, data);
};

// Test all endpoints
window.testAllAPIs = function() {
    return new APITester();
};

// Auto-run API tests if requested
if (window.location.search.includes('test-api')) {
    setTimeout(() => new APITester(), 2000);
}
