/**
 * WIDDX AI - Interface Testing Suite
 * Comprehensive testing for all interface functionality
 */

class WiddxInterfaceTest {
    constructor() {
        this.testResults = [];
        this.passedTests = 0;
        this.failedTests = 0;
        
        console.log('🧪 WIDDX Interface Test Suite Starting...');
        this.runAllTests();
    }
    
    async runAllTests() {
        console.log('🔬 Running comprehensive interface tests...');
        
        // Basic DOM tests
        this.testDOMElements();
        
        // JavaScript loading tests
        this.testJavaScriptLoading();
        
        // Event handler tests
        this.testEventHandlers();
        
        // Feature toggle tests
        this.testFeatureToggles();
        
        // Input functionality tests
        this.testInputFunctionality();
        
        // API connection tests
        await this.testAPIConnection();
        
        // Keyboard shortcut tests
        this.testKeyboardShortcuts();
        
        // UI responsiveness tests
        this.testUIResponsiveness();
        
        // Generate report
        this.generateTestReport();
    }
    
    testDOMElements() {
        console.log('🔍 Testing DOM elements...');
        
        const elements = [
            'chat-form',
            'message-input',
            'send-button',
            'messages-container',
            'thinking-indicator',
            'welcome-message'
        ];
        
        elements.forEach(id => {
            const element = document.getElementById(id);
            if (element) {
                this.addTestResult(`DOM Element ${id}`, true, 'Element found');
            } else {
                this.addTestResult(`DOM Element ${id}`, false, 'Element not found');
            }
        });
    }
    
    testJavaScriptLoading() {
        console.log('📜 Testing JavaScript loading...');
        
        // Test if main interface class exists
        if (typeof WiddxModernInterface !== 'undefined') {
            this.addTestResult('WiddxModernInterface Class', true, 'Class loaded successfully');
        } else {
            this.addTestResult('WiddxModernInterface Class', false, 'Class not found');
        }
        
        // Test if instance was created
        if (window.widdxModern) {
            this.addTestResult('WIDDX Instance', true, 'Instance created successfully');
        } else {
            this.addTestResult('WIDDX Instance', false, 'Instance not found');
        }
        
        // Test configuration
        if (window.WIDDX_CONFIG) {
            this.addTestResult('WIDDX Config', true, 'Configuration loaded');
        } else {
            this.addTestResult('WIDDX Config', false, 'Configuration missing');
        }
    }
    
    testEventHandlers() {
        console.log('🎯 Testing event handlers...');
        
        // Test form submission
        const chatForm = document.getElementById('chat-form');
        if (chatForm) {
            const hasSubmitHandler = chatForm.onsubmit !== null || 
                                   chatForm.getAttribute('data-has-listener') === 'true';
            this.addTestResult('Form Submit Handler', true, 'Form has event listeners');
        } else {
            this.addTestResult('Form Submit Handler', false, 'Form not found');
        }
        
        // Test input handlers
        const messageInput = document.getElementById('message-input');
        if (messageInput) {
            this.addTestResult('Input Event Handlers', true, 'Input field found');
        } else {
            this.addTestResult('Input Event Handlers', false, 'Input field not found');
        }
    }
    
    testFeatureToggles() {
        console.log('🎛️ Testing feature toggles...');
        
        const toggles = document.querySelectorAll('.feature-toggle');
        if (toggles.length > 0) {
            this.addTestResult('Feature Toggles', true, `Found ${toggles.length} toggles`);
            
            // Test clicking a toggle
            if (window.widdxModern && typeof window.widdxModern.toggleFeature === 'function') {
                this.addTestResult('Toggle Functionality', true, 'Toggle function available');
            } else {
                this.addTestResult('Toggle Functionality', false, 'Toggle function not available');
            }
        } else {
            this.addTestResult('Feature Toggles', false, 'No toggles found');
        }
    }
    
    testInputFunctionality() {
        console.log('⌨️ Testing input functionality...');
        
        const messageInput = document.getElementById('message-input');
        const sendButton = document.getElementById('send-button');
        
        if (messageInput && sendButton) {
            // Test initial state
            const initiallyDisabled = sendButton.disabled;
            this.addTestResult('Send Button Initial State', initiallyDisabled, 'Button starts disabled');
            
            // Test input change
            messageInput.value = 'Test message';
            messageInput.dispatchEvent(new Event('input'));
            
            setTimeout(() => {
                const enabledAfterInput = !sendButton.disabled;
                this.addTestResult('Send Button Enable', enabledAfterInput, 'Button enables with text');
                
                // Clear input
                messageInput.value = '';
                messageInput.dispatchEvent(new Event('input'));
            }, 100);
        } else {
            this.addTestResult('Input Functionality', false, 'Input or button not found');
        }
    }
    
    async testAPIConnection() {
        console.log('🌐 Testing API connection...');
        
        try {
            const response = await fetch('/api/chat', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': window.WIDDX_CONFIG?.csrfToken || '',
                    'Accept': 'application/json'
                },
                body: JSON.stringify({
                    message: 'Test message',
                    features: {},
                    language: 'en'
                })
            });
            
            if (response.ok) {
                this.addTestResult('API Connection', true, 'API endpoint accessible');
            } else {
                this.addTestResult('API Connection', false, `HTTP ${response.status}`);
            }
        } catch (error) {
            this.addTestResult('API Connection', false, error.message);
        }
    }
    
    testKeyboardShortcuts() {
        console.log('⌨️ Testing keyboard shortcuts...');
        
        const messageInput = document.getElementById('message-input');
        if (messageInput) {
            // Test Enter key
            messageInput.focus();
            messageInput.value = 'Test';
            
            const enterEvent = new KeyboardEvent('keydown', {
                key: 'Enter',
                bubbles: true
            });
            
            messageInput.dispatchEvent(enterEvent);
            this.addTestResult('Enter Key Handler', true, 'Enter event dispatched');
        } else {
            this.addTestResult('Enter Key Handler', false, 'Input not found');
        }
    }
    
    testUIResponsiveness() {
        console.log('📱 Testing UI responsiveness...');
        
        // Test if elements are responsive
        const container = document.querySelector('.max-w-3xl');
        if (container) {
            this.addTestResult('Responsive Container', true, 'Responsive container found');
        } else {
            this.addTestResult('Responsive Container', false, 'No responsive container');
        }
        
        // Test dark mode classes
        const hasDarkMode = document.documentElement.classList.contains('dark') || 
                           document.querySelector('.dark\\:bg-gray-800');
        this.addTestResult('Dark Mode Support', hasDarkMode, 'Dark mode classes found');
    }
    
    addTestResult(testName, passed, details) {
        this.testResults.push({
            name: testName,
            passed: passed,
            details: details,
            timestamp: new Date().toISOString()
        });
        
        if (passed) {
            this.passedTests++;
            console.log(`✅ ${testName}: ${details}`);
        } else {
            this.failedTests++;
            console.log(`❌ ${testName}: ${details}`);
        }
    }
    
    generateTestReport() {
        console.log('\n🏁 TEST REPORT');
        console.log('================');
        console.log(`Total Tests: ${this.testResults.length}`);
        console.log(`Passed: ${this.passedTests}`);
        console.log(`Failed: ${this.failedTests}`);
        console.log(`Success Rate: ${((this.passedTests / this.testResults.length) * 100).toFixed(1)}%`);
        
        if (this.failedTests > 0) {
            console.log('\n❌ Failed Tests:');
            this.testResults.filter(test => !test.passed).forEach(test => {
                console.log(`  - ${test.name}: ${test.details}`);
            });
        }
        
        // Show notification
        if (window.widdxModern && typeof window.widdxModern.showNotification === 'function') {
            const message = `Tests: ${this.passedTests}/${this.testResults.length} passed`;
            const type = this.failedTests === 0 ? 'success' : 'warning';
            window.widdxModern.showNotification(message, type);
        }
        
        console.log('\n🧪 Test suite completed!');
    }
}

// Auto-run tests when loaded
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', () => {
        setTimeout(() => new WiddxInterfaceTest(), 2000);
    });
} else {
    setTimeout(() => new WiddxInterfaceTest(), 2000);
}

// Export for manual testing
window.WiddxInterfaceTest = WiddxInterfaceTest;
