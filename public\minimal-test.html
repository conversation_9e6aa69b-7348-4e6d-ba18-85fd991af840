<!DOCTYPE html>
<html>
<head>
    <title>WIDDX Minimal Test</title>
    <meta charset="utf-8">
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background: #f0f0f0; }
        .container { max-width: 600px; margin: 0 auto; background: white; padding: 20px; border-radius: 10px; }
        .messages { height: 300px; overflow-y: auto; border: 1px solid #ddd; padding: 10px; margin: 10px 0; background: #fafafa; }
        .message { margin: 10px 0; padding: 10px; border-radius: 5px; }
        .user { background: #007bff; color: white; text-align: right; }
        .bot { background: #e9ecef; color: #333; }
        .input-area { display: flex; gap: 10px; }
        input { flex: 1; padding: 10px; border: 1px solid #ddd; border-radius: 5px; }
        button { padding: 10px 20px; background: #007bff; color: white; border: none; border-radius: 5px; cursor: pointer; }
        button:disabled { background: #ccc; cursor: not-allowed; }
        .status { margin: 10px 0; padding: 10px; background: #d1ecf1; border-radius: 5px; font-size: 14px; }
        .thinking { display: none; padding: 10px; background: #fff3cd; border-radius: 5px; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧪 WIDDX Minimal Test</h1>
        
        <div class="status" id="status">Loading...</div>
        
        <div class="messages" id="messages">
            <div class="message bot">Welcome! Type "مرحبا" or "hello" to test.</div>
        </div>
        
        <div class="thinking" id="thinking">🤔 WIDDX is thinking...</div>
        
        <div class="input-area">
            <input type="text" id="messageInput" placeholder="Type your message..." />
            <button id="sendButton" disabled>Send</button>
        </div>
    </div>

    <script>
        console.log('🚀 Minimal test starting...');
        
        let isTyping = false;
        let sessionId = null;
        
        // Elements
        const messageInput = document.getElementById('messageInput');
        const sendButton = document.getElementById('sendButton');
        const messages = document.getElementById('messages');
        const thinking = document.getElementById('thinking');
        const status = document.getElementById('status');
        
        // Setup
        function init() {
            console.log('🔧 Setting up...');
            
            // Event listeners
            sendButton.addEventListener('click', sendMessage);
            messageInput.addEventListener('keydown', (e) => {
                if (e.key === 'Enter') {
                    e.preventDefault();
                    sendMessage();
                }
            });
            messageInput.addEventListener('input', updateSendButton);
            
            // Test API
            testAPI();
            
            console.log('✅ Setup complete');
        }
        
        async function testAPI() {
            try {
                const response = await fetch('/api/chat/health');
                const data = await response.json();
                
                if (data.status === 'ok') {
                    status.textContent = '✅ API Working - Ready to chat!';
                    status.style.background = '#d4edda';
                } else {
                    status.textContent = '❌ API Error';
                    status.style.background = '#f8d7da';
                }
            } catch (error) {
                status.textContent = '❌ API Connection Failed';
                status.style.background = '#f8d7da';
                console.error('API test failed:', error);
            }
        }
        
        function updateSendButton() {
            const hasText = messageInput.value.trim().length > 0;
            sendButton.disabled = !hasText || isTyping;
        }
        
        async function sendMessage() {
            const message = messageInput.value.trim();
            if (!message || isTyping) return;
            
            console.log('📤 Sending:', message);
            
            // Add user message
            addMessage(message, 'user');
            
            // Clear input
            messageInput.value = '';
            updateSendButton();
            
            // Show thinking
            showThinking();
            
            try {
                // Detect language
                const isArabic = /[\u0600-\u06FF]/.test(message);
                
                // Call API
                const response = await fetch('/api/chat', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Accept': 'application/json'
                    },
                    body: JSON.stringify({
                        message: message,
                        language: isArabic ? 'ar' : 'en',
                        features: {},
                        session_id: sessionId
                    })
                });
                
                console.log('📡 Response status:', response.status);
                
                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}`);
                }
                
                const result = await response.json();
                console.log('✅ API Result:', result);
                
                hideThinking();
                
                if (result.success) {
                    addMessage(result.message, 'bot');
                    if (result.session_id) {
                        sessionId = result.session_id;
                    }
                } else {
                    addMessage('Error: ' + (result.error || 'Unknown error'), 'bot');
                }
                
            } catch (error) {
                console.error('❌ Error:', error);
                hideThinking();
                
                // Simple fallback
                const isArabic = /[\u0600-\u06FF]/.test(message);
                const fallback = isArabic 
                    ? 'عذراً، حدث خطأ في الاتصال. أنا WIDDX AI.'
                    : 'Sorry, connection error occurred. I\'m WIDDX AI.';
                addMessage(fallback, 'bot');
            }
        }
        
        function addMessage(text, sender) {
            const messageDiv = document.createElement('div');
            messageDiv.className = `message ${sender}`;
            messageDiv.textContent = text;
            messages.appendChild(messageDiv);
            messages.scrollTop = messages.scrollHeight;
            
            console.log(`💬 Added ${sender} message:`, text);
        }
        
        function showThinking() {
            isTyping = true;
            thinking.style.display = 'block';
            updateSendButton();
        }
        
        function hideThinking() {
            isTyping = false;
            thinking.style.display = 'none';
            updateSendButton();
        }
        
        // Start when page loads
        document.addEventListener('DOMContentLoaded', init);
        
        console.log('📄 Minimal test script loaded');
    </script>
</body>
</html>
