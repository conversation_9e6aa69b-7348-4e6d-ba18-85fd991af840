/**
 * Debug API - Test API directly
 */

console.log('🔍 Debug API Starting...');

async function debugAPI() {
    console.log('🚀 Testing API directly...');
    
    // Test 1: Health Check
    console.log('1️⃣ Testing Health Check...');
    try {
        const healthResponse = await fetch('/api/chat/health');
        const healthData = await healthResponse.json();
        console.log('✅ Health Check:', healthData);
    } catch (error) {
        console.error('❌ Health Check Error:', error);
    }
    
    // Test 2: Chat API with Arabic
    console.log('2️⃣ Testing Chat API with Arabic...');
    try {
        const chatResponse = await fetch('/api/chat', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'Accept': 'application/json'
            },
            body: JSON.stringify({
                message: 'مرحبا',
                features: {},
                language: 'ar'
            })
        });
        
        console.log('📡 Chat Response Status:', chatResponse.status);
        console.log('📡 Chat Response Headers:', chatResponse.headers);
        
        const chatData = await chatResponse.json();
        console.log('✅ Chat Response:', chatData);
        
        if (chatData.success && chatData.message) {
            console.log('🎉 API is working! Response:', chatData.message);
        } else {
            console.error('❌ API returned unsuccessful response');
        }
    } catch (error) {
        console.error('❌ Chat API Error:', error);
    }
    
    // Test 3: Chat API with English
    console.log('3️⃣ Testing Chat API with English...');
    try {
        const chatResponse = await fetch('/api/chat', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'Accept': 'application/json'
            },
            body: JSON.stringify({
                message: 'hello',
                features: {},
                language: 'en'
            })
        });
        
        const chatData = await chatResponse.json();
        console.log('✅ English Chat Response:', chatData);
    } catch (error) {
        console.error('❌ English Chat API Error:', error);
    }
    
    // Test 4: Test WIDDX sendToAPI function
    console.log('4️⃣ Testing WIDDX sendToAPI function...');
    if (window.widdxModern && typeof window.widdxModern.sendToAPI === 'function') {
        try {
            const testData = {
                message: 'Test from WIDDX function',
                features: {},
                language: 'en'
            };
            
            const result = await window.widdxModern.sendToAPI(testData);
            console.log('✅ WIDDX sendToAPI Result:', result);
        } catch (error) {
            console.error('❌ WIDDX sendToAPI Error:', error);
        }
    } else {
        console.error('❌ WIDDX sendToAPI function not found');
    }
    
    console.log('🏁 Debug API completed!');
}

// Auto-run debug after a delay
setTimeout(() => {
    debugAPI();
}, 2000);

// Export for manual testing
window.debugAPI = debugAPI;
