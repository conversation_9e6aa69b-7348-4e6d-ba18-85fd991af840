/**
 * WIDDX AI - Advanced Modern Chat Interface
 * Enhanced with cutting-edge features and animations
 */

class WiddxModernInterface {
    constructor() {
        console.log('🚀 Initializing WIDDX Modern Interface...');

        // Check if config exists
        if (!window.WIDDX_CONFIG) {
            console.error('❌ WIDDX_CONFIG not found! Creating default config...');
            window.WIDDX_CONFIG = {
                apiUrl: window.location.origin,
                csrfToken: '',
                currentLanguage: 'en',
                features: {
                    search: false,
                    deepSearch: false,
                    thinkMode: false,
                    imageGeneration: false,
                    vision: false
                }
            };
        }

        this.config = window.WIDDX_CONFIG;
        this.features = { ...this.config.features };
        this.isTyping = false;
        this.messageHistory = [];
        this.chatHistory = this.loadChatHistory();
        this.settings = this.loadSettings();
        this.isRecording = false;
        this.mediaRecorder = null;
        this.currentChatId = null;
        this.typingTimer = null;
        this.lastActivity = Date.now();

        console.log('✅ WIDDX Config loaded:', this.config);
        console.log('✅ Features initialized:', this.features);

        this.init();
    }

    init() {
        console.log('🔧 Starting WIDDX initialization...');

        try {
            this.setupEventListeners();
            this.setupFeatureToggles();
            this.setupThemeToggle();
            this.setupLanguageSelector();
            this.setupAutoResize();
            this.setupKeyboardShortcuts();
            this.setupAdvancedFeatures();
            this.setupVoiceInput();
            this.setupFileUpload();
            this.setupEmojiPicker();
            this.setupChatHistory();
            this.setupSettings();
            this.startActivityMonitoring();
            this.loadUserPreferences();

            console.log('🚀 WIDDX Advanced Interface initialized with all features');

            // Test basic functionality
            this.testBasicFunctionality();

        } catch (error) {
            console.error('❌ Error during initialization:', error);
            throw error;
        }
    }

    testBasicFunctionality() {
        console.log('🧪 Testing basic functionality...');

        // Test feature toggles
        const toggles = document.querySelectorAll('.feature-toggle');
        console.log(`✅ Found ${toggles.length} feature toggles`);

        // Test form
        const form = document.getElementById('chat-form');
        console.log(`✅ Chat form: ${form ? 'Found' : 'Not found'}`);

        // Test input
        const input = document.getElementById('message-input');
        console.log(`✅ Message input: ${input ? 'Found' : 'Not found'}`);

        // Test send button
        const sendBtn = document.getElementById('send-button');
        console.log(`✅ Send button: ${sendBtn ? 'Found' : 'Not found'}`);

        console.log('🧪 Basic functionality test complete');
    }

    setupEventListeners() {
        console.log('🔧 Setting up event listeners...');

        // Chat form submission
        const chatForm = document.getElementById('chat-form');
        if (chatForm) {
            chatForm.addEventListener('submit', (e) => this.handleSubmit(e));
            console.log('✅ Chat form listener added');
        } else {
            console.warn('⚠️ Chat form not found');
        }

        // New chat button
        const newChatBtn = document.getElementById('new-chat-btn');
        if (newChatBtn) {
            newChatBtn.addEventListener('click', () => this.newChat());
            console.log('✅ New chat button listener added');
        } else {
            console.warn('⚠️ New chat button not found');
        }

        // Input field
        const messageInput = document.getElementById('message-input');
        if (messageInput) {
            messageInput.addEventListener('input', () => this.handleInputChange());
            console.log('✅ Message input listener added');
        } else {
            console.warn('⚠️ Message input not found');
        }

        // Feature cards in welcome message
        document.querySelectorAll('.feature-card').forEach(card => {
            card.addEventListener('click', (e) => {
                const feature = e.currentTarget.dataset.feature;
                if (feature) {
                    this.toggleFeature(feature);
                }
            });
        });

        // Character count
        const messageInput = document.getElementById('message-input');
        if (messageInput) {
            messageInput.addEventListener('input', () => {
                this.updateCharCount();
                this.showClearButton();
                this.autoSaveDraft();
            });
        }

        // Clear input button
        const clearInput = document.getElementById('clear-input');
        if (clearInput) {
            clearInput.addEventListener('click', () => this.clearInput());
        }

        // Suggestion buttons
        document.querySelectorAll('.suggestion-btn').forEach(btn => {
            btn.addEventListener('click', (e) => {
                const suggestion = e.target.textContent.replace(/"/g, '');
                this.insertSuggestion(suggestion);
            });
        });
    }

    setupFeatureToggles() {
        console.log('🔧 Setting up feature toggles...');

        const toggles = document.querySelectorAll('.feature-toggle');
        console.log(`Found ${toggles.length} feature toggles`);

        toggles.forEach((toggle, index) => {
            const feature = toggle.dataset.feature;
            console.log(`Setting up toggle ${index + 1}: ${feature}`);

            toggle.addEventListener('click', (e) => {
                e.preventDefault();
                console.log(`🎯 Feature toggle clicked: ${feature}`);
                this.toggleFeature(feature);
            });
        });

        if (toggles.length === 0) {
            console.warn('⚠️ No feature toggles found!');
        } else {
            console.log('✅ All feature toggles set up successfully');
        }
    }

    toggleFeature(feature) {
        console.log(`🎯 Toggling feature: ${feature}`);

        if (!feature) {
            console.error('❌ No feature specified');
            return;
        }

        if (!this.features.hasOwnProperty(feature)) {
            console.error(`❌ Unknown feature: ${feature}`);
            return;
        }

        // Toggle the feature
        this.features[feature] = !this.features[feature];
        console.log(`✅ Feature ${feature} is now: ${this.features[feature] ? 'ON' : 'OFF'}`);

        // Update all toggle buttons for this feature
        const elements = document.querySelectorAll(`[data-feature="${feature}"]`);
        console.log(`Found ${elements.length} elements for feature ${feature}`);

        elements.forEach(element => {
            if (this.features[feature]) {
                element.classList.add('active');
            } else {
                element.classList.remove('active');
            }
        });

        // Update active features display
        this.updateActiveFeatures();

        // Show notification
        this.showNotification(
            `${this.getFeatureName(feature)} ${this.features[feature] ? 'activated' : 'deactivated'}`,
            this.features[feature] ? 'success' : 'info'
        );
    }

    getFeatureName(feature) {
        const names = {
            search: 'Web Search',
            deepSearch: 'Deep Search',
            thinkMode: 'Think Mode',
            imageGeneration: 'Image Generation',
            vision: 'Vision Analysis'
        };
        return names[feature] || feature;
    }

    updateActiveFeatures() {
        const activeFeatures = Object.keys(this.features).filter(f => this.features[f]);
        const activeFeaturesElement = document.getElementById('active-features');
        const featuresListElement = document.getElementById('features-list');

        // Update header display
        if (activeFeatures.length > 0) {
            activeFeaturesElement.classList.remove('hidden');
            featuresListElement.textContent = activeFeatures.map(f => this.getFeatureName(f)).join(', ');
        } else {
            activeFeaturesElement.classList.add('hidden');
        }

        // Update active features bar
        const activeFeaturesBar = document.getElementById('active-features-bar');
        const activeFeaturesList = document.getElementById('active-features-list');

        if (activeFeatures.length > 0) {
            activeFeaturesBar.classList.remove('hidden');
            activeFeaturesList.innerHTML = activeFeatures.map(feature =>
                `<span class="px-2 py-1 bg-widdx-100 dark:bg-widdx-800 text-widdx-700 dark:text-widdx-300 text-xs rounded-full">
                    ${this.getFeatureName(feature)}
                </span>`
            ).join('');
        } else {
            activeFeaturesBar.classList.add('hidden');
        }

        // Update progress bar
        this.updateFeaturesProgress();

        // Update statistics
        this.updateFeatureStats();
    }

    updateFeaturesProgress() {
        const totalFeatures = Object.keys(this.features).length;
        const activeFeatures = Object.keys(this.features).filter(f => this.features[f]).length;
        const percentage = (activeFeatures / totalFeatures) * 100;

        const progressBar = document.getElementById('features-progress');
        const activeCount = document.getElementById('active-count');

        if (progressBar) {
            progressBar.style.width = `${percentage}%`;
        }

        if (activeCount) {
            activeCount.textContent = `${activeFeatures}/${totalFeatures}`;
        }
    }

    updateFeatureStats() {
        // This could be expanded to show more detailed statistics
        console.log(`Features active: ${Object.keys(this.features).filter(f => this.features[f]).length}/5`);
    }

    setupThemeToggle() {
        const themeToggle = document.getElementById('theme-toggle');
        if (themeToggle) {
            themeToggle.addEventListener('click', () => {
                document.documentElement.classList.toggle('dark');
                localStorage.setItem('theme', document.documentElement.classList.contains('dark') ? 'dark' : 'light');
            });
        }

        // Load saved theme
        const savedTheme = localStorage.getItem('theme');
        if (savedTheme === 'dark' || (!savedTheme && window.matchMedia('(prefers-color-scheme: dark)').matches)) {
            document.documentElement.classList.add('dark');
        }
    }

    setupLanguageSelector() {
        const languageSelect = document.getElementById('language-select');
        if (languageSelect) {
            languageSelect.addEventListener('change', (e) => {
                this.changeLanguage(e.target.value);
            });
        }
    }

    changeLanguage(language) {
        this.config.currentLanguage = language;
        this.updatePlaceholders(language);

        // Update text direction
        const isRTL = ['ar', 'he', 'fa', 'ur'].includes(language);
        document.documentElement.dir = isRTL ? 'rtl' : 'ltr';

        console.log(`Language changed to: ${language}`);
    }

    updatePlaceholders(language) {
        const placeholders = {
            en: 'Ask WIDDX anything... (Ctrl+Enter to send)',
            ar: 'اسأل WIDDX أي شيء... (Ctrl+Enter للإرسال)',
            es: 'Pregunta a WIDDX cualquier cosa... (Ctrl+Enter para enviar)',
            fr: 'Demandez à WIDDX n\'importe quoi... (Ctrl+Enter pour envoyer)',
            de: 'Fragen Sie WIDDX alles... (Ctrl+Enter zum Senden)',
            zh: '向WIDDX询问任何问题... (Ctrl+Enter发送)',
            ja: 'WIDDXに何でも聞いてください... (Ctrl+Enterで送信)',
            ko: 'WIDDX에게 무엇이든 물어보세요... (Ctrl+Enter로 전송)',
            ru: 'Спросите WIDDX о чем угодно... (Ctrl+Enter для отправки)'
        };

        const messageInput = document.getElementById('message-input');
        if (messageInput && placeholders[language]) {
            messageInput.placeholder = placeholders[language];
        }
    }

    setupAutoResize() {
        const textarea = document.getElementById('message-input');
        if (!textarea) return;

        textarea.addEventListener('input', function() {
            this.style.height = 'auto';
            this.style.height = Math.min(this.scrollHeight, 120) + 'px';
        });
    }

    setupKeyboardShortcuts() {
        document.addEventListener('keydown', (e) => {
            // Feature toggles with Alt + number
            if (e.altKey) {
                const shortcuts = {
                    '1': 'search',
                    '2': 'deepSearch',
                    '3': 'thinkMode',
                    '4': 'imageGeneration',
                    '5': 'vision'
                };

                if (shortcuts[e.key]) {
                    e.preventDefault();
                    this.toggleFeature(shortcuts[e.key]);
                }
            }

            // Ctrl/Cmd + Enter to send
            if ((e.ctrlKey || e.metaKey) && e.key === 'Enter') {
                e.preventDefault();
                this.submitMessage();
            }

            // Ctrl/Cmd + K for new chat
            if ((e.ctrlKey || e.metaKey) && e.key === 'k') {
                e.preventDefault();
                this.newChat();
            }
        });
    }

    handleSubmit(e) {
        e.preventDefault();
        this.submitMessage();
    }

    async submitMessage() {
        const messageInput = document.getElementById('message-input');
        const message = messageInput?.value.trim();

        if (!message || this.isTyping) return;

        // Hide welcome message
        this.hideWelcome();

        // Add user message
        this.addMessage(message, 'user');

        // Clear input
        messageInput.value = '';
        messageInput.style.height = 'auto';
        this.updateSendButton();

        // Show thinking indicator
        this.showThinking();

        try {
            // Prepare request data
            const requestData = {
                message: message,
                features: this.features,
                language: this.config.currentLanguage
            };

            // Send to API
            const response = await this.sendToAPI(requestData);

            // Hide thinking indicator
            this.hideThinking();

            // Add assistant response
            this.addMessage(response.message || 'Hello! I\'m WIDDX AI. How can I help you today?', 'assistant', response);

        } catch (error) {
            console.error('Error sending message:', error);
            this.hideThinking();
            this.addMessage('Sorry, I encountered an error. Please try again.', 'assistant', { error: true });
        }
    }

    async sendToAPI(data) {
        console.log('🚀 Sending to API:', data);

        try {
            // Get CSRF token
            const csrfToken = this.config.csrfToken ||
                             document.querySelector('meta[name="csrf-token"]')?.getAttribute('content') ||
                             document.querySelector('input[name="_token"]')?.value;

            const headers = {
                'Content-Type': 'application/json',
                'Accept': 'application/json'
            };

            // Add CSRF token if available
            if (csrfToken) {
                headers['X-CSRF-TOKEN'] = csrfToken;
            }

            const response = await fetch('/api/chat', {
                method: 'POST',
                headers: headers,
                body: JSON.stringify(data)
            });

            console.log('📡 Response status:', response.status);

            if (!response.ok) {
                const errorText = await response.text();
                console.error('API Error Response:', errorText);
                throw new Error(`HTTP error! status: ${response.status}`);
            }

            const result = await response.json();
            console.log('✅ API Response:', result);

            if (!result.success) {
                throw new Error(result.error || 'API returned unsuccessful response');
            }

            return result;
        } catch (error) {
            console.error('API Request failed:', error);
            console.warn('🔄 Falling back to simulation...');
            // Fallback to simulation
            return await this.simulateAPICall(data);
        }
    }

    async simulateAPICall(data) {
        console.log('🔄 Using fallback simulation for:', data);
        // Simulate network delay
        await new Promise(resolve => setTimeout(resolve, 500 + Math.random() * 1000));

        // Generate response based on language
        const isArabic = data.language === 'ar' || /[\u0600-\u06FF]/.test(data.message);
        const message = data.message.toLowerCase();

        let response = '';
        if (isArabic) {
            if (message.includes('مرحبا') || message.includes('السلام') || message.includes('أهلا')) {
                response = 'مرحباً بك! أنا WIDDX AI، مساعدك الذكي. كيف يمكنني مساعدتك اليوم؟';
            } else if (message.includes('صورة') || message.includes('رسم')) {
                response = 'يمكنني مساعدتك في توليد الصور! فعّل ميزة "توليد الصور" وسأرسم لك ما تريد.';
            } else {
                response = 'شكراً لك على رسالتك! أنا WIDDX AI وأنا هنا لمساعدتك. يمكنك تفعيل الميزات المختلفة للحصول على إمكانيات متقدمة.';
            }
        } else {
            if (message.includes('hello') || message.includes('hi')) {
                response = 'Hello! I\'m WIDDX AI, your intelligent assistant. How can I help you today?';
            } else if (message.includes('image') || message.includes('picture')) {
                response = 'I can help you generate images! Enable the "Image Generation" feature and I\'ll create what you need.';
            } else if (message.includes('test')) {
                response = 'I received your test message! I\'m WIDDX AI and I\'m working correctly. Try asking me something in Arabic or English!';
            } else {
                response = 'Thank you for your message! I\'m WIDDX AI and I\'m here to help you. Enable different features to get advanced capabilities.';
            }
        }

        return {
            success: true,
            message: response,
            session_id: 'sim_' + Date.now(),
            language: data.language,
            features_used: Object.keys(data.features || {}).filter(f => data.features[f])
        };
    }

    addMessage(content, sender, metadata = {}) {
        const messagesContainer = document.getElementById('messages-container');
        if (!messagesContainer) return;

        const messageElement = this.createMessageElement(content, sender, metadata);
        messagesContainer.appendChild(messageElement);

        // Scroll to bottom
        this.scrollToBottom();

        // Save to history
        this.messageHistory.push({
            content,
            sender,
            metadata,
            timestamp: Date.now()
        });
    }

    createMessageElement(content, sender, metadata = {}) {
        const messageDiv = document.createElement('div');
        messageDiv.className = `animate-slide-in mb-6 max-w-3xl mx-auto`;

        const isUser = sender === 'user';
        const alignClass = isUser ? 'justify-end' : 'justify-start';

        messageDiv.innerHTML = `
            <div class="flex ${alignClass}">
                <div class="flex items-start space-x-3 max-w-2xl ${isUser ? 'flex-row-reverse space-x-reverse' : ''}">
                    <div class="w-8 h-8 rounded-full flex items-center justify-center flex-shrink-0 ${
                        isUser
                            ? 'bg-gray-300 dark:bg-gray-600'
                            : 'bg-gradient-to-br from-widdx-500 to-widdx-600'
                    }">
                        <span class="text-sm font-bold ${isUser ? 'text-gray-700 dark:text-gray-300' : 'text-white'}">
                            ${isUser ? 'U' : 'W'}
                        </span>
                    </div>
                    <div class="flex-1 min-w-0">
                        <div class="p-4 rounded-lg ${
                            isUser
                                ? 'bg-widdx-500 text-white'
                                : 'bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700'
                        }">
                            <div class="text-sm ${isUser ? 'text-white' : 'text-gray-900 dark:text-white'}">
                                ${this.formatMessage(content)}
                            </div>
                        </div>
                        <div class="mt-1 text-xs text-gray-500 dark:text-gray-400 ${isUser ? 'text-right' : 'text-left'}">
                            ${new Date().toLocaleTimeString()}
                            ${Object.keys(this.features).filter(f => this.features[f]).length > 0 ?
                                ` • ${Object.keys(this.features).filter(f => this.features[f]).map(f => this.getFeatureName(f)).join(', ')}` :
                                ''
                            }
                        </div>
                    </div>
                </div>
            </div>
        `;

        return messageDiv;
    }

    formatMessage(content) {
        // Basic markdown-like formatting
        return content
            .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')
            .replace(/\*(.*?)\*/g, '<em>$1</em>')
            .replace(/`(.*?)`/g, '<code class="bg-gray-100 dark:bg-gray-700 px-1 rounded">$1</code>')
            .replace(/\n/g, '<br>');
    }

    showThinking() {
        this.isTyping = true;
        const thinkingIndicator = document.getElementById('thinking-indicator');
        if (thinkingIndicator) {
            thinkingIndicator.classList.remove('hidden');
            this.scrollToBottom();
        }
    }

    hideThinking() {
        this.isTyping = false;
        const thinkingIndicator = document.getElementById('thinking-indicator');
        if (thinkingIndicator) {
            thinkingIndicator.classList.add('hidden');
        }
    }

    hideWelcome() {
        const welcomeMessage = document.getElementById('welcome-message');
        if (welcomeMessage) {
            welcomeMessage.style.display = 'none';
        }
    }

    scrollToBottom() {
        const messagesContainer = document.getElementById('messages-container');
        if (messagesContainer) {
            messagesContainer.scrollTop = messagesContainer.scrollHeight;
        }
    }

    handleInputChange() {
        this.updateSendButton();
    }

    updateSendButton() {
        const messageInput = document.getElementById('message-input');
        const sendButton = document.getElementById('send-button');

        if (messageInput && sendButton) {
            sendButton.disabled = !messageInput.value.trim();
        }
    }

    newChat() {
        // Reset features
        Object.keys(this.features).forEach(feature => {
            this.features[feature] = false;
        });

        // Update UI
        document.querySelectorAll('.feature-toggle').forEach(toggle => {
            toggle.classList.remove('active');
        });

        // Clear messages
        const messagesContainer = document.getElementById('messages-container');
        if (messagesContainer) {
            messagesContainer.innerHTML = `
                <div id="welcome-message" class="max-w-3xl mx-auto text-center py-12">
                    <!-- Welcome content will be restored here -->
                </div>
            `;
        }

        // Clear input
        const messageInput = document.getElementById('message-input');
        if (messageInput) {
            messageInput.value = '';
            messageInput.style.height = 'auto';
        }

        // Update features display
        this.updateActiveFeatures();
        this.updateSendButton();

        this.showNotification('New chat started', 'success');
        console.log('New chat started - all features reset');
    }

    // Advanced Features
    setupAdvancedFeatures() {
        // Reset features button
        const resetFeatures = document.getElementById('reset-features');
        if (resetFeatures) {
            resetFeatures.addEventListener('click', () => this.resetAllFeatures());
        }

        // Clear features button
        const clearFeatures = document.getElementById('clear-features');
        if (clearFeatures) {
            clearFeatures.addEventListener('click', () => this.resetAllFeatures());
        }

        // Settings toggle
        const settingsToggle = document.getElementById('settings-toggle');
        if (settingsToggle) {
            settingsToggle.addEventListener('click', () => this.toggleAdvancedSettings());
        }
    }

    resetAllFeatures() {
        Object.keys(this.features).forEach(feature => {
            this.features[feature] = false;
        });

        document.querySelectorAll('.feature-toggle').forEach(toggle => {
            toggle.classList.remove('active');
        });

        this.updateActiveFeatures();
        this.showNotification('All features reset', 'info');
    }

    toggleAdvancedSettings() {
        const advancedSettings = document.getElementById('advanced-settings');
        if (advancedSettings) {
            advancedSettings.classList.toggle('hidden');
        }
    }

    // Character count and input management
    updateCharCount() {
        const messageInput = document.getElementById('message-input');
        const charCount = document.getElementById('char-count');

        if (messageInput && charCount) {
            const count = messageInput.value.length;
            charCount.textContent = count;

            // Change color based on limit
            if (count > 3500) {
                charCount.className = 'text-red-500';
            } else if (count > 3000) {
                charCount.className = 'text-yellow-500';
            } else {
                charCount.className = 'text-gray-500 dark:text-gray-400';
            }
        }
    }

    showClearButton() {
        const messageInput = document.getElementById('message-input');
        const clearButton = document.getElementById('clear-input');

        if (messageInput && clearButton) {
            if (messageInput.value.length > 0) {
                clearButton.classList.remove('hidden');
                clearButton.classList.add('flex');
            } else {
                clearButton.classList.add('hidden');
                clearButton.classList.remove('flex');
            }
        }
    }

    clearInput() {
        const messageInput = document.getElementById('message-input');
        if (messageInput) {
            messageInput.value = '';
            messageInput.style.height = 'auto';
            this.updateCharCount();
            this.showClearButton();
            this.updateSendButton();
            messageInput.focus();
        }
    }

    insertSuggestion(suggestion) {
        const messageInput = document.getElementById('message-input');
        if (messageInput) {
            messageInput.value = suggestion;
            messageInput.focus();
            this.updateCharCount();
            this.showClearButton();
            this.updateSendButton();

            // Hide suggestions
            const suggestions = document.getElementById('suggestions');
            if (suggestions) {
                suggestions.classList.add('hidden');
            }
        }
    }

    autoSaveDraft() {
        const messageInput = document.getElementById('message-input');
        if (messageInput && messageInput.value.trim()) {
            localStorage.setItem('widdx_draft', messageInput.value);
        } else {
            localStorage.removeItem('widdx_draft');
        }
    }

    loadDraft() {
        const draft = localStorage.getItem('widdx_draft');
        const messageInput = document.getElementById('message-input');

        if (draft && messageInput) {
            messageInput.value = draft;
            this.updateCharCount();
            this.showClearButton();
            this.updateSendButton();
        }
    }

    showNotification(message, type = 'info') {
        // Create notification element
        const notification = document.createElement('div');
        notification.className = `fixed top-4 right-4 z-50 p-4 rounded-lg shadow-lg transition-all duration-300 transform translate-x-full`;

        const bgColor = {
            success: 'bg-green-500',
            error: 'bg-red-500',
            warning: 'bg-yellow-500',
            info: 'bg-blue-500'
        }[type] || 'bg-blue-500';

        notification.className += ` ${bgColor} text-white`;
        notification.textContent = message;

        document.body.appendChild(notification);

        // Animate in
        setTimeout(() => {
            notification.classList.remove('translate-x-full');
        }, 100);

        // Auto remove
        setTimeout(() => {
            notification.classList.add('translate-x-full');
            setTimeout(() => {
                if (notification.parentElement) {
                    notification.remove();
                }
            }, 300);
        }, 3000);
    }
}

    // Voice Input Setup
    setupVoiceInput() {
        const voiceInput = document.getElementById('voice-input');
        const voiceFab = document.getElementById('voice-fab');

        if (voiceInput) {
            voiceInput.addEventListener('click', () => this.toggleVoiceRecording());
        }

        if (voiceFab) {
            voiceFab.addEventListener('click', () => this.toggleVoiceRecording());
        }

        // Check for speech recognition support
        if ('webkitSpeechRecognition' in window || 'SpeechRecognition' in window) {
            this.setupSpeechRecognition();
        }
    }

    setupSpeechRecognition() {
        const SpeechRecognition = window.SpeechRecognition || window.webkitSpeechRecognition;
        this.recognition = new SpeechRecognition();

        this.recognition.continuous = false;
        this.recognition.interimResults = true;
        this.recognition.lang = this.config.currentLanguage || 'en-US';

        this.recognition.onstart = () => {
            this.isRecording = true;
            this.updateVoiceUI(true);
        };

        this.recognition.onresult = (event) => {
            let transcript = '';
            for (let i = event.resultIndex; i < event.results.length; i++) {
                transcript += event.results[i][0].transcript;
            }

            const messageInput = document.getElementById('message-input');
            if (messageInput) {
                messageInput.value = transcript;
                this.updateCharCount();
                this.showClearButton();
                this.updateSendButton();
            }
        };

        this.recognition.onend = () => {
            this.isRecording = false;
            this.updateVoiceUI(false);
        };

        this.recognition.onerror = (event) => {
            console.error('Speech recognition error:', event.error);
            this.isRecording = false;
            this.updateVoiceUI(false);
            this.showNotification('Voice recognition error', 'error');
        };
    }

    toggleVoiceRecording() {
        if (!this.recognition) {
            this.showNotification('Voice input not supported', 'warning');
            return;
        }

        if (this.isRecording) {
            this.recognition.stop();
        } else {
            this.recognition.start();
        }
    }

    updateVoiceUI(recording) {
        const voiceInput = document.getElementById('voice-input');
        const voiceFab = document.getElementById('voice-fab');

        if (recording) {
            voiceInput?.classList.add('recording');
            voiceFab?.classList.add('recording');
        } else {
            voiceInput?.classList.remove('recording');
            voiceFab?.classList.remove('recording');
        }
    }

    // File Upload Setup
    setupFileUpload() {
        const attachFile = document.getElementById('attach-file');
        const fileUploadModal = document.getElementById('file-upload-modal');
        const closeUploadModal = document.getElementById('close-upload-modal');
        const browseFiles = document.getElementById('browse-files');
        const fileInput = document.getElementById('file-input');

        if (attachFile) {
            attachFile.addEventListener('click', () => {
                fileUploadModal?.classList.remove('hidden');
                fileUploadModal?.classList.add('flex');
            });
        }

        if (closeUploadModal) {
            closeUploadModal.addEventListener('click', () => {
                fileUploadModal?.classList.add('hidden');
                fileUploadModal?.classList.remove('flex');
            });
        }

        if (browseFiles && fileInput) {
            browseFiles.addEventListener('click', () => fileInput.click());
        }

        if (fileInput) {
            fileInput.addEventListener('change', (e) => this.handleFileUpload(e));
        }
    }

    handleFileUpload(event) {
        const files = Array.from(event.target.files);

        files.forEach(file => {
            if (file.size > 10 * 1024 * 1024) { // 10MB limit
                this.showNotification(`File ${file.name} is too large (max 10MB)`, 'error');
                return;
            }

            this.showNotification(`File ${file.name} uploaded successfully`, 'success');
        });

        // Close modal
        const fileUploadModal = document.getElementById('file-upload-modal');
        fileUploadModal?.classList.add('hidden');
        fileUploadModal?.classList.remove('flex');
    }

    // Emoji Picker Setup
    setupEmojiPicker() {
        const emojiPicker = document.getElementById('emoji-picker');
        const emojiModal = document.getElementById('emoji-modal');
        const closeEmojiModal = document.getElementById('close-emoji-modal');

        if (emojiPicker) {
            emojiPicker.addEventListener('click', () => {
                emojiModal?.classList.remove('hidden');
                emojiModal?.classList.add('flex');
                this.populateEmojis();
            });
        }

        if (closeEmojiModal) {
            closeEmojiModal.addEventListener('click', () => {
                emojiModal?.classList.add('hidden');
                emojiModal?.classList.remove('flex');
            });
        }
    }

    populateEmojis() {
        const emojis = ['😀', '😃', '😄', '😁', '😆', '😅', '😂', '🤣', '😊', '😇', '🙂', '🙃', '😉', '😌', '😍', '🥰', '😘', '😗', '😙', '😚', '😋', '😛', '😝', '😜', '🤪', '🤨', '🧐', '🤓', '😎', '🤩', '🥳', '😏', '😒', '😞', '😔', '😟', '😕', '🙁', '☹️', '😣', '😖', '😫', '😩', '🥺', '😢', '😭', '😤', '😠', '😡', '🤬', '🤯', '😳', '🥵', '🥶', '😱', '😨', '😰', '😥', '😓', '🤗', '🤔', '🤭', '🤫', '🤥', '😶', '😐', '😑', '😬', '🙄', '😯', '😦', '😧', '😮', '😲'];

        const emojiModal = document.querySelector('#emoji-modal .grid');
        if (emojiModal) {
            emojiModal.innerHTML = emojis.map(emoji =>
                `<button class="p-2 hover:bg-gray-100 dark:hover:bg-gray-700 rounded text-lg" onclick="window.widdxModern.insertEmoji('${emoji}')">${emoji}</button>`
            ).join('');
        }
    }

    insertEmoji(emoji) {
        const messageInput = document.getElementById('message-input');
        if (messageInput) {
            const cursorPos = messageInput.selectionStart;
            const textBefore = messageInput.value.substring(0, cursorPos);
            const textAfter = messageInput.value.substring(cursorPos);

            messageInput.value = textBefore + emoji + textAfter;
            messageInput.focus();
            messageInput.setSelectionRange(cursorPos + emoji.length, cursorPos + emoji.length);

            this.updateCharCount();
            this.showClearButton();
            this.updateSendButton();
        }

        // Close modal
        const emojiModal = document.getElementById('emoji-modal');
        emojiModal?.classList.add('hidden');
        emojiModal?.classList.remove('flex');
    }

    // Chat History Management
    setupChatHistory() {
        const searchChats = document.getElementById('search-chats');
        const clearHistory = document.getElementById('clear-history');
        const chatSearchInput = document.getElementById('chat-search-input');

        if (searchChats) {
            searchChats.addEventListener('click', () => this.toggleChatSearch());
        }

        if (clearHistory) {
            clearHistory.addEventListener('click', () => this.clearChatHistory());
        }

        if (chatSearchInput) {
            chatSearchInput.addEventListener('input', (e) => this.searchChatHistory(e.target.value));
        }

        this.updateChatStats();
    }

    toggleChatSearch() {
        const searchContainer = document.getElementById('chat-search-container');
        const searchInput = document.getElementById('chat-search-input');

        if (searchContainer) {
            searchContainer.classList.toggle('hidden');
            if (!searchContainer.classList.contains('hidden')) {
                searchInput?.focus();
            }
        }
    }

    clearChatHistory() {
        if (confirm('Are you sure you want to clear all chat history?')) {
            this.chatHistory = [];
            this.saveChatHistory();
            this.updateChatStats();
            this.showNotification('Chat history cleared', 'success');
        }
    }

    searchChatHistory(query) {
        // Implementation for searching chat history
        console.log('Searching chats for:', query);
    }

    updateChatStats() {
        const totalChats = document.getElementById('total-chats');
        const todayChats = document.getElementById('today-chats');
        const weekChats = document.getElementById('week-chats');

        const today = new Date().toDateString();
        const weekAgo = new Date(Date.now() - 7 * 24 * 60 * 60 * 1000);

        const todayCount = this.chatHistory.filter(chat =>
            new Date(chat.timestamp).toDateString() === today
        ).length;

        const weekCount = this.chatHistory.filter(chat =>
            new Date(chat.timestamp) >= weekAgo
        ).length;

        if (totalChats) totalChats.textContent = this.chatHistory.length;
        if (todayChats) todayChats.textContent = todayCount;
        if (weekChats) weekChats.textContent = weekCount;
    }

    // Settings Management
    setupSettings() {
        // Voice input toggle
        const voiceInputToggle = document.getElementById('voice-input-toggle');
        if (voiceInputToggle) {
            voiceInputToggle.checked = this.settings.voiceInput;
            voiceInputToggle.addEventListener('change', (e) => {
                this.settings.voiceInput = e.target.checked;
                this.saveSettings();
            });
        }

        // Sound effects toggle
        const soundEffectsToggle = document.getElementById('sound-effects-toggle');
        if (soundEffectsToggle) {
            soundEffectsToggle.checked = this.settings.soundEffects;
            soundEffectsToggle.addEventListener('change', (e) => {
                this.settings.soundEffects = e.target.checked;
                this.saveSettings();
            });
        }

        // Animations toggle
        const animationsToggle = document.getElementById('animations-toggle');
        if (animationsToggle) {
            animationsToggle.checked = this.settings.animations;
            animationsToggle.addEventListener('change', (e) => {
                this.settings.animations = e.target.checked;
                this.saveSettings();
                this.toggleAnimations(e.target.checked);
            });
        }

        // Compact mode toggle
        const compactModeToggle = document.getElementById('compact-mode-toggle');
        if (compactModeToggle) {
            compactModeToggle.checked = this.settings.compactMode;
            compactModeToggle.addEventListener('change', (e) => {
                this.settings.compactMode = e.target.checked;
                this.saveSettings();
                this.toggleCompactMode(e.target.checked);
            });
        }
    }

    toggleAnimations(enabled) {
        if (enabled) {
            document.body.classList.remove('no-animations');
        } else {
            document.body.classList.add('no-animations');
        }
    }

    toggleCompactMode(enabled) {
        if (enabled) {
            document.body.classList.add('compact-mode');
        } else {
            document.body.classList.remove('compact-mode');
        }
    }

    // Data Management
    loadChatHistory() {
        try {
            return JSON.parse(localStorage.getItem('widdx_chat_history') || '[]');
        } catch {
            return [];
        }
    }

    saveChatHistory() {
        localStorage.setItem('widdx_chat_history', JSON.stringify(this.chatHistory));
    }

    loadSettings() {
        try {
            return {
                voiceInput: false,
                soundEffects: false,
                animations: true,
                compactMode: false,
                saveHistory: true,
                analytics: false,
                ...JSON.parse(localStorage.getItem('widdx_settings') || '{}')
            };
        } catch {
            return {
                voiceInput: false,
                soundEffects: false,
                animations: true,
                compactMode: false,
                saveHistory: true,
                analytics: false
            };
        }
    }

    saveSettings() {
        localStorage.setItem('widdx_settings', JSON.stringify(this.settings));
    }

    loadUserPreferences() {
        // Load draft
        this.loadDraft();

        // Apply settings
        this.toggleAnimations(this.settings.animations);
        this.toggleCompactMode(this.settings.compactMode);
    }

    // Activity Monitoring
    startActivityMonitoring() {
        setInterval(() => {
            const now = Date.now();
            if (now - this.lastActivity > 300000) { // 5 minutes
                this.showIdleMessage();
            }
        }, 60000); // Check every minute

        // Update last activity on user interaction
        document.addEventListener('click', () => this.lastActivity = Date.now());
        document.addEventListener('keypress', () => this.lastActivity = Date.now());
    }

    showIdleMessage() {
        // Could show a gentle reminder or tip
        console.log('User has been idle for 5 minutes');
    }
}

// Initialize the interface when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    try {
        console.log('🔧 DOM loaded, initializing WIDDX...');
        window.widdxModern = new WiddxModernInterface();
        console.log('🚀 WIDDX Advanced Interface Ready with all features!');
    } catch (error) {
        console.error('❌ Failed to initialize WIDDX:', error);

        // Fallback initialization
        console.log('🔧 Attempting fallback initialization...');

        // Basic feature toggle functionality
        document.querySelectorAll('.feature-toggle').forEach(toggle => {
            toggle.addEventListener('click', function(e) {
                e.preventDefault();
                this.classList.toggle('active');

                const feature = this.dataset.feature;
                const isActive = this.classList.contains('active');

                console.log(`Feature ${feature}: ${isActive ? 'ON' : 'OFF'}`);

                // Simple notification
                const notification = document.createElement('div');
                notification.className = 'fixed top-4 right-4 z-50 p-4 bg-blue-500 text-white rounded-lg shadow-lg';
                notification.textContent = `${feature} ${isActive ? 'activated' : 'deactivated'}`;
                document.body.appendChild(notification);

                setTimeout(() => notification.remove(), 3000);
            });
        });

        // Basic form submission
        const chatForm = document.getElementById('chat-form');
        if (chatForm) {
            chatForm.addEventListener('submit', function(e) {
                e.preventDefault();
                const input = document.getElementById('message-input');
                if (input && input.value.trim()) {
                    console.log('Message:', input.value);

                    // Simple response simulation
                    const messagesContainer = document.getElementById('messages-container');
                    if (messagesContainer) {
                        const messageDiv = document.createElement('div');
                        messageDiv.className = 'mb-4 p-4 bg-gray-100 rounded-lg';
                        messageDiv.innerHTML = `
                            <strong>You:</strong> ${input.value}<br>
                            <strong>WIDDX:</strong> Hello! I received your message: "${input.value}"
                        `;
                        messagesContainer.appendChild(messageDiv);
                        messagesContainer.scrollTop = messagesContainer.scrollHeight;
                    }

                    input.value = '';
                }
            });
        }

        console.log('✅ Fallback initialization complete');
    }
});
