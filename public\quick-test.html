<!DOCTYPE html>
<html>
<head>
    <title>Quick API Test</title>
    <meta charset="utf-8">
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background: #f5f5f5; }
        .container { max-width: 600px; margin: 0 auto; background: white; padding: 20px; border-radius: 10px; }
        .test-btn { padding: 10px 20px; margin: 10px; background: #007bff; color: white; border: none; border-radius: 5px; cursor: pointer; }
        .test-btn:hover { background: #0056b3; }
        .result { margin: 10px 0; padding: 15px; border-radius: 5px; white-space: pre-wrap; }
        .success { background: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .error { background: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        .info { background: #d1ecf1; color: #0c5460; border: 1px solid #bee5eb; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧪 Quick API Test</h1>
        
        <button class="test-btn" onclick="testHealth()">Test Health</button>
        <button class="test-btn" onclick="testArabic()">Test Arabic</button>
        <button class="test-btn" onclick="testEnglish()">Test English</button>
        <button class="test-btn" onclick="clearResults()">Clear</button>
        
        <div id="results"></div>
    </div>

    <script>
        function addResult(message, type = 'info') {
            const results = document.getElementById('results');
            const div = document.createElement('div');
            div.className = `result ${type}`;
            div.textContent = new Date().toLocaleTimeString() + ' - ' + message;
            results.appendChild(div);
            results.scrollTop = results.scrollHeight;
        }

        async function testHealth() {
            addResult('Testing health endpoint...', 'info');
            try {
                const response = await fetch('/api/chat/health');
                const data = await response.json();
                addResult('Health: ' + JSON.stringify(data, null, 2), 'success');
            } catch (error) {
                addResult('Health Error: ' + error.message, 'error');
            }
        }

        async function testArabic() {
            addResult('Testing Arabic message...', 'info');
            try {
                const response = await fetch('/api/chat', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Accept': 'application/json'
                    },
                    body: JSON.stringify({
                        message: 'مرحبا',
                        language: 'ar',
                        features: {}
                    })
                });
                
                const data = await response.json();
                if (data.success) {
                    addResult('Arabic Response: ' + data.message, 'success');
                    addResult('Full Response: ' + JSON.stringify(data, null, 2), 'info');
                } else {
                    addResult('Arabic Error: ' + JSON.stringify(data, null, 2), 'error');
                }
            } catch (error) {
                addResult('Arabic Request Error: ' + error.message, 'error');
            }
        }

        async function testEnglish() {
            addResult('Testing English message...', 'info');
            try {
                const response = await fetch('/api/chat', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Accept': 'application/json'
                    },
                    body: JSON.stringify({
                        message: 'hello',
                        language: 'en',
                        features: {}
                    })
                });
                
                const data = await response.json();
                if (data.success) {
                    addResult('English Response: ' + data.message, 'success');
                    addResult('Full Response: ' + JSON.stringify(data, null, 2), 'info');
                } else {
                    addResult('English Error: ' + JSON.stringify(data, null, 2), 'error');
                }
            } catch (error) {
                addResult('English Request Error: ' + error.message, 'error');
            }
        }

        function clearResults() {
            document.getElementById('results').innerHTML = '';
        }

        // Auto-test on load
        window.addEventListener('load', () => {
            addResult('Quick API Test loaded. Click buttons to test.', 'info');
        });
    </script>
</body>
</html>
