<!DOCTYPE html>
<html lang="en" class="h-full">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="csrf-token" content="<?php echo e(csrf_token()); ?>">
    <title>WIDDX AI - Modern Chat Interface</title>

    <!-- Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">

    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.2/css/all.min.css">

    <!-- Tai<PERSON>wind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>

    <!-- Advanced Styles -->
    <link rel="stylesheet" href="<?php echo e(asset('css/widdx-advanced.css')); ?>">

    <script>
        tailwind.config = {
            darkMode: 'class',
            theme: {
                extend: {
                    fontFamily: {
                        'sans': ['Inter', 'system-ui', 'sans-serif'],
                    },
                    colors: {
                        'widdx': {
                            50: '#fff7ed',
                            100: '#ffedd5',
                            200: '#fed7aa',
                            300: '#fdba74',
                            400: '#fb923c',
                            500: '#f97316',
                            600: '#ea580c',
                            700: '#c2410c',
                            800: '#9a3412',
                            900: '#7c2d12',
                        }
                    }
                }
            }
        }
    </script>

    <style>
        /* Custom scrollbar */
        .custom-scrollbar::-webkit-scrollbar {
            width: 6px;
        }
        .custom-scrollbar::-webkit-scrollbar-track {
            background: transparent;
        }
        .custom-scrollbar::-webkit-scrollbar-thumb {
            background: rgba(156, 163, 175, 0.5);
            border-radius: 3px;
        }
        .custom-scrollbar::-webkit-scrollbar-thumb:hover {
            background: rgba(156, 163, 175, 0.8);
        }

        /* Advanced Animations */
        @keyframes slideIn {
            from { opacity: 0; transform: translateY(10px); }
            to { opacity: 1; transform: translateY(0); }
        }

        @keyframes fadeInScale {
            from { opacity: 0; transform: scale(0.95); }
            to { opacity: 1; transform: scale(1); }
        }

        @keyframes pulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.5; }
        }

        @keyframes glow {
            0%, 100% { box-shadow: 0 0 20px rgba(249, 115, 22, 0.3); }
            50% { box-shadow: 0 0 30px rgba(249, 115, 22, 0.6); }
        }

        @keyframes typing {
            0%, 100% { transform: translateY(0); }
            50% { transform: translateY(-5px); }
        }

        .animate-slide-in {
            animation: slideIn 0.3s ease-out;
        }

        .animate-fade-in-scale {
            animation: fadeInScale 0.4s ease-out;
        }

        .animate-glow {
            animation: glow 2s ease-in-out infinite;
        }

        .animate-typing {
            animation: typing 1.4s ease-in-out infinite;
        }

        /* Feature toggle styles */
        .feature-toggle {
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            position: relative;
            overflow: hidden;
        }

        .feature-toggle::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
            transition: left 0.5s ease;
        }

        .feature-toggle:hover::before {
            left: 100%;
        }

        .feature-toggle.active {
            background: linear-gradient(135deg, #f97316, #ea580c);
            color: white;
            box-shadow: 0 4px 20px rgba(249, 115, 22, 0.4);
            transform: translateY(-2px);
        }

        .feature-toggle.active::after {
            content: '✓';
            position: absolute;
            top: 2px;
            right: 6px;
            font-size: 10px;
            font-weight: bold;
            color: white;
            background: rgba(255, 255, 255, 0.2);
            border-radius: 50%;
            width: 16px;
            height: 16px;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .feature-toggle:hover {
            transform: translateY(-1px);
            box-shadow: 0 6px 20px rgba(0, 0, 0, 0.15);
        }

        /* Message bubbles */
        .message-bubble {
            transition: all 0.3s ease;
        }

        .message-bubble:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
        }

        /* Glassmorphism effect */
        .glass {
            backdrop-filter: blur(10px);
            background: rgba(255, 255, 255, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .glass-dark {
            backdrop-filter: blur(10px);
            background: rgba(0, 0, 0, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.1);
        }

        /* Floating action button */
        .fab {
            position: fixed;
            bottom: 100px;
            right: 20px;
            width: 56px;
            height: 56px;
            border-radius: 50%;
            background: linear-gradient(135deg, #f97316, #ea580c);
            color: white;
            border: none;
            box-shadow: 0 4px 20px rgba(249, 115, 22, 0.4);
            cursor: pointer;
            transition: all 0.3s ease;
            z-index: 1000;
        }

        .fab:hover {
            transform: scale(1.1);
            box-shadow: 0 6px 25px rgba(249, 115, 22, 0.6);
        }

        /* Voice recording animation */
        .recording {
            animation: pulse 1s ease-in-out infinite;
            background: #ef4444 !important;
        }

        /* Code syntax highlighting */
        .code-block {
            background: #1e293b;
            color: #e2e8f0;
            border-radius: 8px;
            padding: 16px;
            font-family: 'JetBrains Mono', monospace;
            overflow-x: auto;
        }

        /* Progress bar */
        .progress-bar {
            height: 3px;
            background: linear-gradient(90deg, #f97316, #ea580c);
            border-radius: 2px;
            transition: width 0.3s ease;
        }

        /* Tooltip */
        .tooltip {
            position: relative;
        }

        .tooltip::after {
            content: attr(data-tooltip);
            position: absolute;
            bottom: 100%;
            left: 50%;
            transform: translateX(-50%);
            background: rgba(0, 0, 0, 0.8);
            color: white;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            white-space: nowrap;
            opacity: 0;
            pointer-events: none;
            transition: opacity 0.3s ease;
            z-index: 1000;
        }

        .tooltip:hover::after {
            opacity: 1;
        }
    </style>
</head>
<body class="h-full bg-gray-50 dark:bg-gray-900 font-sans">
    <div class="flex h-full">
        <!-- Sidebar -->
        <div class="hidden lg:flex lg:w-80 lg:flex-col">
            <div class="flex flex-col flex-grow bg-white dark:bg-gray-800 border-r border-gray-200 dark:border-gray-700">
                <!-- Logo -->
                <div class="flex items-center px-6 py-4 border-b border-gray-200 dark:border-gray-700">
                    <div class="flex items-center space-x-3">
                        <div class="w-8 h-8 bg-gradient-to-br from-widdx-500 to-widdx-600 rounded-lg flex items-center justify-center">
                            <span class="text-white font-bold text-lg">W</span>
                        </div>
                        <div>
                            <h1 class="text-xl font-bold text-gray-900 dark:text-white">WIDDX AI</h1>
                            <p class="text-sm text-gray-500 dark:text-gray-400">Intelligent Assistant</p>
                        </div>
                    </div>
                </div>

                <!-- New Chat Button -->
                <div class="p-4">
                    <button id="new-chat-btn" class="w-full flex items-center justify-center px-4 py-3 bg-widdx-500 hover:bg-widdx-600 text-white rounded-lg transition-colors duration-200">
                        <i class="fas fa-plus mr-2"></i>
                        New Chat
                    </button>
                </div>

                <!-- Feature Toggles -->
                <div class="px-4 pb-4">
                    <div class="flex items-center justify-between mb-3">
                        <h3 class="text-sm font-medium text-gray-700 dark:text-gray-300">AI Features</h3>
                        <button id="reset-features" class="text-xs text-gray-500 hover:text-widdx-500 transition-colors" title="Reset all features">
                            <i class="fas fa-undo"></i>
                        </button>
                    </div>
                    <div class="space-y-2">
                        <button class="feature-toggle tooltip w-full flex items-center px-3 py-2 text-sm bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 rounded-lg" data-feature="search" data-tooltip="Real-time web search">
                            <i class="fas fa-search w-4 mr-3"></i>
                            <span class="flex-1 text-left">Web Search</span>
                            <span class="text-xs opacity-60">Alt+1</span>
                        </button>
                        <button class="feature-toggle tooltip w-full flex items-center px-3 py-2 text-sm bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 rounded-lg" data-feature="deepSearch" data-tooltip="Advanced AI-powered search">
                            <i class="fas fa-search-plus w-4 mr-3"></i>
                            <span class="flex-1 text-left">Deep Search</span>
                            <span class="text-xs opacity-60">Alt+2</span>
                        </button>
                        <button class="feature-toggle tooltip w-full flex items-center px-3 py-2 text-sm bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 rounded-lg" data-feature="thinkMode" data-tooltip="Step-by-step reasoning">
                            <i class="fas fa-brain w-4 mr-3"></i>
                            <span class="flex-1 text-left">Think Mode</span>
                            <span class="text-xs opacity-60">Alt+3</span>
                        </button>
                        <button class="feature-toggle tooltip w-full flex items-center px-3 py-2 text-sm bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 rounded-lg" data-feature="imageGeneration" data-tooltip="Create images from text">
                            <i class="fas fa-image w-4 mr-3"></i>
                            <span class="flex-1 text-left">Image Generation</span>
                            <span class="text-xs opacity-60">Alt+4</span>
                        </button>
                        <button class="feature-toggle tooltip w-full flex items-center px-3 py-2 text-sm bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 rounded-lg" data-feature="vision" data-tooltip="Analyze and understand images">
                            <i class="fas fa-eye w-4 mr-3"></i>
                            <span class="flex-1 text-left">Vision Analysis</span>
                            <span class="text-xs opacity-60">Alt+5</span>
                        </button>
                    </div>

                    <!-- Feature Statistics -->
                    <div class="mt-4 p-3 bg-gray-50 dark:bg-gray-700 rounded-lg">
                        <div class="flex items-center justify-between text-xs text-gray-600 dark:text-gray-400">
                            <span>Active Features</span>
                            <span id="active-count" class="font-medium">0/5</span>
                        </div>
                        <div class="mt-2 bg-gray-200 dark:bg-gray-600 rounded-full h-1">
                            <div id="features-progress" class="progress-bar h-1 rounded-full" style="width: 0%"></div>
                        </div>
                    </div>
                </div>

                <!-- Chat History -->
                <div class="flex-1 px-4 pb-4">
                    <div class="flex items-center justify-between mb-3">
                        <h3 class="text-sm font-medium text-gray-700 dark:text-gray-300">Recent Chats</h3>
                        <div class="flex items-center space-x-2">
                            <button id="search-chats" class="text-xs text-gray-500 hover:text-widdx-500 transition-colors" title="Search chats">
                                <i class="fas fa-search"></i>
                            </button>
                            <button id="clear-history" class="text-xs text-gray-500 hover:text-red-500 transition-colors" title="Clear history">
                                <i class="fas fa-trash"></i>
                            </button>
                        </div>
                    </div>

                    <!-- Search Input -->
                    <div id="chat-search-container" class="hidden mb-3">
                        <input
                            type="text"
                            id="chat-search-input"
                            placeholder="Search conversations..."
                            class="w-full px-3 py-2 text-sm bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-widdx-500 focus:border-transparent"
                        >
                    </div>

                    <div id="chat-history-list" class="space-y-1 custom-scrollbar overflow-y-auto max-h-64">
                        <!-- Chat history items will be populated here -->
                        <div id="no-chats" class="text-sm text-gray-500 dark:text-gray-400 text-center py-8">
                            <i class="fas fa-comments text-2xl mb-2 opacity-50"></i>
                            <div>No previous chats</div>
                            <div class="text-xs mt-1">Start a conversation to see history</div>
                        </div>
                    </div>

                    <!-- Chat Statistics -->
                    <div class="mt-4 p-3 bg-gray-50 dark:bg-gray-700 rounded-lg">
                        <div class="text-xs text-gray-600 dark:text-gray-400 space-y-1">
                            <div class="flex justify-between">
                                <span>Total Chats</span>
                                <span id="total-chats">0</span>
                            </div>
                            <div class="flex justify-between">
                                <span>Today</span>
                                <span id="today-chats">0</span>
                            </div>
                            <div class="flex justify-between">
                                <span>This Week</span>
                                <span id="week-chats">0</span>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Settings -->
                <div class="p-4 border-t border-gray-200 dark:border-gray-700">
                    <!-- Language and Theme -->
                    <div class="flex items-center justify-between mb-4">
                        <select id="language-select" class="text-sm bg-transparent text-gray-700 dark:text-gray-300 border-none focus:ring-0 cursor-pointer">
                            <option value="en">🇺🇸 English</option>
                            <option value="ar">🇸🇦 العربية</option>
                            <option value="es">🇪🇸 Español</option>
                            <option value="fr">🇫🇷 Français</option>
                            <option value="de">🇩🇪 Deutsch</option>
                            <option value="zh">🇨🇳 中文</option>
                            <option value="ja">🇯🇵 日本語</option>
                            <option value="ko">🇰🇷 한국어</option>
                            <option value="ru">🇷🇺 Русский</option>
                        </select>
                        <div class="flex items-center space-x-2">
                            <button id="settings-toggle" class="p-2 text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200 tooltip" data-tooltip="Settings">
                                <i class="fas fa-cog"></i>
                            </button>
                            <button id="theme-toggle" class="p-2 text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200 tooltip" data-tooltip="Toggle theme">
                                <i class="fas fa-moon dark:hidden"></i>
                                <i class="fas fa-sun hidden dark:inline"></i>
                            </button>
                        </div>
                    </div>

                    <!-- Advanced Settings Panel -->
                    <div id="advanced-settings" class="hidden space-y-3">
                        <!-- Voice Settings -->
                        <div class="p-3 bg-gray-50 dark:bg-gray-700 rounded-lg">
                            <h4 class="text-xs font-medium text-gray-700 dark:text-gray-300 mb-2">Voice & Audio</h4>
                            <div class="space-y-2">
                                <label class="flex items-center justify-between text-xs">
                                    <span class="text-gray-600 dark:text-gray-400">Enable Voice Input</span>
                                    <input type="checkbox" id="voice-input-toggle" class="rounded border-gray-300 text-widdx-500 focus:ring-widdx-500">
                                </label>
                                <label class="flex items-center justify-between text-xs">
                                    <span class="text-gray-600 dark:text-gray-400">Sound Effects</span>
                                    <input type="checkbox" id="sound-effects-toggle" class="rounded border-gray-300 text-widdx-500 focus:ring-widdx-500">
                                </label>
                            </div>
                        </div>

                        <!-- Display Settings -->
                        <div class="p-3 bg-gray-50 dark:bg-gray-700 rounded-lg">
                            <h4 class="text-xs font-medium text-gray-700 dark:text-gray-300 mb-2">Display</h4>
                            <div class="space-y-2">
                                <label class="flex items-center justify-between text-xs">
                                    <span class="text-gray-600 dark:text-gray-400">Animations</span>
                                    <input type="checkbox" id="animations-toggle" checked class="rounded border-gray-300 text-widdx-500 focus:ring-widdx-500">
                                </label>
                                <label class="flex items-center justify-between text-xs">
                                    <span class="text-gray-600 dark:text-gray-400">Compact Mode</span>
                                    <input type="checkbox" id="compact-mode-toggle" class="rounded border-gray-300 text-widdx-500 focus:ring-widdx-500">
                                </label>
                            </div>
                        </div>

                        <!-- Privacy Settings -->
                        <div class="p-3 bg-gray-50 dark:bg-gray-700 rounded-lg">
                            <h4 class="text-xs font-medium text-gray-700 dark:text-gray-300 mb-2">Privacy</h4>
                            <div class="space-y-2">
                                <label class="flex items-center justify-between text-xs">
                                    <span class="text-gray-600 dark:text-gray-400">Save Chat History</span>
                                    <input type="checkbox" id="save-history-toggle" checked class="rounded border-gray-300 text-widdx-500 focus:ring-widdx-500">
                                </label>
                                <label class="flex items-center justify-between text-xs">
                                    <span class="text-gray-600 dark:text-gray-400">Analytics</span>
                                    <input type="checkbox" id="analytics-toggle" class="rounded border-gray-300 text-widdx-500 focus:ring-widdx-500">
                                </label>
                            </div>
                        </div>
                    </div>

                    <!-- User Info -->
                    <div class="mt-4 p-3 bg-gradient-to-r from-widdx-50 to-orange-50 dark:from-widdx-900/20 dark:to-orange-900/20 rounded-lg">
                        <div class="flex items-center space-x-3">
                            <div class="w-8 h-8 bg-gradient-to-br from-widdx-500 to-widdx-600 rounded-full flex items-center justify-center">
                                <span class="text-white font-bold text-sm">U</span>
                            </div>
                            <div class="flex-1 min-w-0">
                                <div class="text-sm font-medium text-gray-900 dark:text-white truncate">Guest User</div>
                                <div class="text-xs text-gray-500 dark:text-gray-400">Free Plan</div>
                            </div>
                            <button class="text-xs text-widdx-600 dark:text-widdx-400 hover:text-widdx-700 dark:hover:text-widdx-300">
                                <i class="fas fa-crown"></i>
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Main Content -->
        <div class="flex-1 flex flex-col min-w-0">
            <!-- Header -->
            <header class="bg-white dark:bg-gray-800 border-b border-gray-200 dark:border-gray-700 px-4 py-3">
                <div class="flex items-center justify-between">
                    <div class="flex items-center space-x-4">
                        <button class="lg:hidden p-2 text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200">
                            <i class="fas fa-bars"></i>
                        </button>
                        <div>
                            <h2 class="text-lg font-semibold text-gray-900 dark:text-white">New Conversation</h2>
                            <div class="flex items-center space-x-2 text-sm text-gray-500 dark:text-gray-400">
                                <span class="w-2 h-2 bg-green-500 rounded-full"></span>
                                <span>Ready</span>
                                <span id="active-features" class="hidden">• Features: <span id="features-list"></span></span>
                            </div>
                        </div>
                    </div>

                    <!-- Quick Feature Toggles -->
                    <div class="hidden md:flex items-center space-x-2">
                        <button class="feature-toggle px-3 py-1.5 text-xs bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 rounded-full" data-feature="search">
                            <i class="fas fa-search mr-1"></i>Search
                        </button>
                        <button class="feature-toggle px-3 py-1.5 text-xs bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 rounded-full" data-feature="thinkMode">
                            <i class="fas fa-brain mr-1"></i>Think
                        </button>
                        <button class="feature-toggle px-3 py-1.5 text-xs bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 rounded-full" data-feature="imageGeneration">
                            <i class="fas fa-image mr-1"></i>Images
                        </button>

                        <?php if(config('app.debug')): ?>
                        <!-- Test Button (Development Only) -->
                        <button id="run-tests" class="px-3 py-1.5 text-xs bg-red-100 dark:bg-red-900 text-red-700 dark:text-red-300 rounded-full hover:bg-red-200 dark:hover:bg-red-800 transition-colors" title="Run Interface Tests">
                            <i class="fas fa-bug mr-1"></i>Test
                        </button>
                        <?php endif; ?>
                    </div>
                </div>
            </header>

            <!-- Chat Area -->
            <main class="flex-1 overflow-hidden">
                <div class="h-full flex flex-col">
                    <!-- Messages -->
                    <div id="messages-container" class="flex-1 overflow-y-auto custom-scrollbar px-4 py-6">
                        <!-- Welcome Message -->
                        <div id="welcome-message" class="max-w-3xl mx-auto text-center py-12">
                            <div class="w-16 h-16 bg-gradient-to-br from-widdx-500 to-widdx-600 rounded-2xl flex items-center justify-center mx-auto mb-6">
                                <span class="text-white font-bold text-2xl">W</span>
                            </div>
                            <h1 class="text-3xl font-bold text-gray-900 dark:text-white mb-4">Welcome to WIDDX AI</h1>
                            <p class="text-lg text-gray-600 dark:text-gray-400 mb-8">
                                Your intelligent assistant with advanced AI capabilities.
                                Activate features manually and start chatting!
                            </p>

                            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 max-w-4xl mx-auto">
                                <div class="feature-card p-6 bg-white dark:bg-gray-800 rounded-xl border border-gray-200 dark:border-gray-700 hover:border-widdx-300 dark:hover:border-widdx-600 transition-colors cursor-pointer" data-feature="search">
                                    <div class="w-12 h-12 bg-blue-100 dark:bg-blue-900 rounded-lg flex items-center justify-center mb-4">
                                        <i class="fas fa-search text-blue-600 dark:text-blue-400 text-xl"></i>
                                    </div>
                                    <h3 class="font-semibold text-gray-900 dark:text-white mb-2">Web Search</h3>
                                    <p class="text-sm text-gray-600 dark:text-gray-400">Real-time internet search for latest information</p>
                                </div>

                                <div class="feature-card p-6 bg-white dark:bg-gray-800 rounded-xl border border-gray-200 dark:border-gray-700 hover:border-widdx-300 dark:hover:border-widdx-600 transition-colors cursor-pointer" data-feature="thinkMode">
                                    <div class="w-12 h-12 bg-purple-100 dark:bg-purple-900 rounded-lg flex items-center justify-center mb-4">
                                        <i class="fas fa-brain text-purple-600 dark:text-purple-400 text-xl"></i>
                                    </div>
                                    <h3 class="font-semibold text-gray-900 dark:text-white mb-2">Think Mode</h3>
                                    <p class="text-sm text-gray-600 dark:text-gray-400">Step-by-step reasoning and analysis</p>
                                </div>

                                <div class="feature-card p-6 bg-white dark:bg-gray-800 rounded-xl border border-gray-200 dark:border-gray-700 hover:border-widdx-300 dark:hover:border-widdx-600 transition-colors cursor-pointer" data-feature="imageGeneration">
                                    <div class="w-12 h-12 bg-green-100 dark:bg-green-900 rounded-lg flex items-center justify-center mb-4">
                                        <i class="fas fa-image text-green-600 dark:text-green-400 text-xl"></i>
                                    </div>
                                    <h3 class="font-semibold text-gray-900 dark:text-white mb-2">Image Generation</h3>
                                    <p class="text-sm text-gray-600 dark:text-gray-400">Create images from text descriptions</p>
                                </div>
                            </div>

                            <div class="mt-8 p-4 bg-widdx-50 dark:bg-widdx-900/20 rounded-lg">
                                <h4 class="font-medium text-widdx-800 dark:text-widdx-200 mb-2">💡 Quick Tips</h4>
                                <ul class="text-sm text-widdx-700 dark:text-widdx-300 space-y-1">
                                    <li>• Features are OFF by default - click to activate</li>
                                    <li>• Use Alt+1-5 for quick feature toggles</li>
                                    <li>• Chat in any language, I'll respond accordingly</li>
                                </ul>
                            </div>
                        </div>

                        <!-- Chat messages will be inserted here -->
                    </div>

                    <!-- Thinking Indicator -->
                    <div id="thinking-indicator" class="hidden px-4 py-2">
                        <div class="max-w-3xl mx-auto">
                            <div class="flex items-center space-x-3 p-4 bg-gray-100 dark:bg-gray-800 rounded-lg">
                                <div class="w-8 h-8 bg-gradient-to-br from-widdx-500 to-widdx-600 rounded-full flex items-center justify-center">
                                    <span class="text-white font-bold text-sm">W</span>
                                </div>
                                <div class="flex-1">
                                    <div class="flex items-center space-x-2">
                                        <span class="text-sm text-gray-600 dark:text-gray-400">WIDDX is thinking</span>
                                        <div class="flex space-x-1">
                                            <div class="w-2 h-2 bg-widdx-500 rounded-full animate-bounce"></div>
                                            <div class="w-2 h-2 bg-widdx-500 rounded-full animate-bounce" style="animation-delay: 0.1s"></div>
                                            <div class="w-2 h-2 bg-widdx-500 rounded-full animate-bounce" style="animation-delay: 0.2s"></div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Input Area -->
                    <div class="border-t border-gray-200 dark:border-gray-700 bg-white dark:bg-gray-800 p-4">
                        <div class="max-w-3xl mx-auto">
                            <!-- Active Features Display -->
                            <div id="active-features-bar" class="hidden mb-3">
                                <div class="flex items-center space-x-2 p-2 bg-widdx-50 dark:bg-widdx-900/20 rounded-lg">
                                    <i class="fas fa-magic text-widdx-500 text-sm"></i>
                                    <span class="text-sm text-widdx-700 dark:text-widdx-300">Active:</span>
                                    <div id="active-features-list" class="flex flex-wrap gap-1"></div>
                                    <button id="clear-features" class="ml-auto text-xs text-widdx-600 hover:text-widdx-800 dark:text-widdx-400 dark:hover:text-widdx-200">
                                        <i class="fas fa-times"></i>
                                    </button>
                                </div>
                            </div>

                            <form id="chat-form" class="relative">
                                <div class="flex items-end space-x-3">
                                    <div class="flex-1 relative">
                                        <!-- Input Actions Bar -->
                                        <div class="flex items-center justify-between mb-2">
                                            <div class="flex items-center space-x-2">
                                                <button type="button" id="attach-file" class="p-1.5 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 tooltip" data-tooltip="Attach file">
                                                    <i class="fas fa-paperclip text-sm"></i>
                                                </button>
                                                <button type="button" id="voice-input" class="p-1.5 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 tooltip" data-tooltip="Voice input">
                                                    <i class="fas fa-microphone text-sm"></i>
                                                </button>
                                                <button type="button" id="emoji-picker" class="p-1.5 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 tooltip" data-tooltip="Add emoji">
                                                    <i class="fas fa-smile text-sm"></i>
                                                </button>
                                            </div>
                                            <div class="flex items-center space-x-2 text-xs text-gray-500 dark:text-gray-400">
                                                <span id="char-count">0</span>
                                                <span>/</span>
                                                <span>4000</span>

                                            </div>
                                        </div>

                                        <div class="relative">
                                            <textarea
                                                id="message-input"
                                                rows="1"
                                                placeholder="Ask WIDDX anything... (Ctrl+Enter to send)"
                                                class="w-full resize-none border border-gray-300 dark:border-gray-600 rounded-lg px-4 py-3 pr-20 bg-white dark:bg-gray-700 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 focus:ring-2 focus:ring-widdx-500 focus:border-transparent transition-all duration-200"
                                                style="max-height: 120px;"
                                                maxlength="4000"
                                            ></textarea>

                                            <!-- Input Actions -->
                                            <div class="absolute right-2 bottom-2 flex items-center space-x-1">
                                                <button type="button" id="clear-input" class="hidden w-6 h-6 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 rounded items-center justify-center tooltip" data-tooltip="Clear">
                                                    <i class="fas fa-times text-xs"></i>
                                                </button>
                                                <button type="submit" id="send-button" disabled class="w-8 h-8 bg-widdx-500 hover:bg-widdx-600 disabled:bg-gray-300 dark:disabled:bg-gray-600 text-white rounded-lg flex items-center justify-center transition-all duration-200 tooltip" data-tooltip="Send message">
                                                    <i class="fas fa-paper-plane text-sm"></i>
                                                </button>
                                            </div>
                                        </div>

                                        <!-- Suggestions -->
                                        <div id="suggestions" class="hidden mt-2">
                                            <div class="flex flex-wrap gap-2">
                                                <button type="button" class="suggestion-btn px-3 py-1 text-xs bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 rounded-full hover:bg-gray-200 dark:hover:bg-gray-600 transition-colors">
                                                    "What can you do?"
                                                </button>
                                                <button type="button" class="suggestion-btn px-3 py-1 text-xs bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 rounded-full hover:bg-gray-200 dark:hover:bg-gray-600 transition-colors">
                                                    "Help me with coding"
                                                </button>
                                                <button type="button" class="suggestion-btn px-3 py-1 text-xs bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 rounded-full hover:bg-gray-200 dark:hover:bg-gray-600 transition-colors">
                                                    "Generate an image"
                                                </button>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
            </main>
        </div>
    </div>

    <!-- Floating Action Button -->
    <button id="voice-fab" class="fab hidden" title="Voice Input">
        <i class="fas fa-microphone"></i>
    </button>

    <!-- File Upload Modal -->
    <div id="file-upload-modal" class="hidden fixed inset-0 bg-black bg-opacity-50 z-50 items-center justify-center">
        <div class="bg-white dark:bg-gray-800 rounded-lg p-6 max-w-md w-full mx-4 animate-fade-in-scale">
            <div class="flex items-center justify-between mb-4">
                <h3 class="text-lg font-semibold text-gray-900 dark:text-white">Upload File</h3>
                <button id="close-upload-modal" class="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="border-2 border-dashed border-gray-300 dark:border-gray-600 rounded-lg p-8 text-center">
                <i class="fas fa-cloud-upload-alt text-4xl text-gray-400 mb-4"></i>
                <p class="text-gray-600 dark:text-gray-400 mb-2">Drag and drop files here</p>
                <p class="text-sm text-gray-500 dark:text-gray-500 mb-4">or click to browse</p>
                <input type="file" id="file-input" class="hidden" multiple accept="image/*,.pdf,.doc,.docx,.txt">
                <button id="browse-files" class="px-4 py-2 bg-widdx-500 text-white rounded-lg hover:bg-widdx-600 transition-colors">
                    Browse Files
                </button>
            </div>
            <div class="mt-4 text-xs text-gray-500 dark:text-gray-400">
                Supported: Images, PDF, DOC, DOCX, TXT (Max 10MB each)
            </div>
        </div>
    </div>

    <!-- Emoji Picker Modal -->
    <div id="emoji-modal" class="hidden fixed inset-0 bg-black bg-opacity-50 z-50 items-center justify-center">
        <div class="bg-white dark:bg-gray-800 rounded-lg p-4 max-w-sm w-full mx-4 animate-fade-in-scale">
            <div class="flex items-center justify-between mb-4">
                <h3 class="text-lg font-semibold text-gray-900 dark:text-white">Add Emoji</h3>
                <button id="close-emoji-modal" class="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="grid grid-cols-8 gap-2 max-h-64 overflow-y-auto custom-scrollbar">
                <!-- Emojis will be populated here -->
            </div>
        </div>
    </div>

    <!-- Settings Modal -->
    <div id="settings-modal" class="hidden fixed inset-0 bg-black bg-opacity-50 z-50 items-center justify-center">
        <div class="bg-white dark:bg-gray-800 rounded-lg p-6 max-w-md w-full mx-4 animate-fade-in-scale">
            <div class="flex items-center justify-between mb-6">
                <h3 class="text-xl font-semibold text-gray-900 dark:text-white">Settings</h3>
                <button id="close-settings-modal" class="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300">
                    <i class="fas fa-times"></i>
                </button>
            </div>

            <div class="space-y-6">
                <!-- Theme Settings -->
                <div>
                    <h4 class="text-sm font-medium text-gray-900 dark:text-white mb-3">Appearance</h4>
                    <div class="flex items-center justify-between">
                        <span class="text-sm text-gray-700 dark:text-gray-300">Dark Mode</span>
                        <button id="theme-toggle-modal" class="relative inline-flex h-6 w-11 items-center rounded-full bg-gray-200 dark:bg-gray-600 transition-colors focus:outline-none focus:ring-2 focus:ring-widdx-500 focus:ring-offset-2">
                            <span class="inline-block h-4 w-4 transform rounded-full bg-white transition-transform dark:translate-x-6"></span>
                        </button>
                    </div>
                </div>

                <!-- Language Settings -->
                <div>
                    <h4 class="text-sm font-medium text-gray-900 dark:text-white mb-3">Language</h4>
                    <select id="language-select-modal" class="w-full p-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white">
                        <option value="en">🇺🇸 English</option>
                        <option value="ar">🇸🇦 العربية</option>
                        <option value="es">🇪🇸 Español</option>
                        <option value="fr">🇫🇷 Français</option>
                        <option value="de">🇩🇪 Deutsch</option>
                        <option value="zh">🇨🇳 中文</option>
                        <option value="ja">🇯🇵 日本語</option>
                        <option value="ko">🇰🇷 한국어</option>
                        <option value="ru">🇷🇺 Русский</option>
                    </select>
                </div>

                <!-- Interface Settings -->
                <div>
                    <h4 class="text-sm font-medium text-gray-900 dark:text-white mb-3">Interface</h4>
                    <div class="space-y-3">
                        <div class="flex items-center justify-between">
                            <span class="text-sm text-gray-700 dark:text-gray-300">Sound Effects</span>
                            <input type="checkbox" id="sound-effects-toggle" class="rounded border-gray-300 text-widdx-600 focus:ring-widdx-500">
                        </div>
                        <div class="flex items-center justify-between">
                            <span class="text-sm text-gray-700 dark:text-gray-300">Animations</span>
                            <input type="checkbox" id="animations-toggle" class="rounded border-gray-300 text-widdx-600 focus:ring-widdx-500" checked>
                        </div>
                        <div class="flex items-center justify-between">
                            <span class="text-sm text-gray-700 dark:text-gray-300">Auto-save Drafts</span>
                            <input type="checkbox" id="autosave-toggle" class="rounded border-gray-300 text-widdx-600 focus:ring-widdx-500" checked>
                        </div>
                    </div>
                </div>

                <!-- Actions -->
                <div class="flex space-x-3 pt-4 border-t border-gray-200 dark:border-gray-600">
                    <button id="reset-settings" class="flex-1 px-4 py-2 text-sm text-gray-700 dark:text-gray-300 bg-gray-100 dark:bg-gray-700 rounded-lg hover:bg-gray-200 dark:hover:bg-gray-600 transition-colors">
                        Reset to Default
                    </button>
                    <button id="save-settings" class="flex-1 px-4 py-2 text-sm text-white bg-widdx-500 rounded-lg hover:bg-widdx-600 transition-colors">
                        Save Changes
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Notification Container -->
    <div id="notification-container" class="fixed top-4 right-4 z-50 space-y-2"></div>

    <!-- Progress Bar -->
    <div id="progress-container" class="hidden fixed top-0 left-0 right-0 z-50">
        <div class="progress-bar" style="width: 0%"></div>
    </div>

    <!-- Scripts -->
    <script>
        // Global configuration
        window.WIDDX_CONFIG = {
            apiUrl: '<?php echo e(config("app.url")); ?>',
            csrfToken: '<?php echo e(csrf_token()); ?>',
            currentLanguage: 'en',
            features: {
                search: false,
                deepSearch: false,
                thinkMode: false,
                imageGeneration: false,
                vision: false
            }
        };

        // Debug logging
        console.log('🔧 WIDDX Config loaded:', window.WIDDX_CONFIG);
    </script>

    <!-- Main WIDDX Interface Script -->
    <!-- WIDDX Simple - Working Version -->
    <script src="<?php echo e(asset('js/widdx-simple.js')); ?>" defer></script>

    <!-- Emergency Override Script -->
    <script>
        console.log('🚨 Emergency override loading...');

        // Override any existing functions
        document.addEventListener('DOMContentLoaded', function() {
            console.log('🔧 Emergency override active');

            // Force setup if WIDDX Simple didn't load
            setTimeout(() => {
                if (!window.widdxSimple) {
                    console.log('⚠️ WIDDX Simple not found, creating emergency version...');
                    setupEmergencyWIDDX();
                }
            }, 3000);
        });

        function setupEmergencyWIDDX() {
            console.log('🚨 Setting up emergency WIDDX...');

            const sendButton = document.getElementById('send-button');
            const messageInput = document.getElementById('message-input');

            if (sendButton && messageInput) {
                // Remove existing listeners
                sendButton.replaceWith(sendButton.cloneNode(true));
                const newSendButton = document.getElementById('send-button');

                // Add new listener
                newSendButton.addEventListener('click', emergencySendMessage);
                messageInput.addEventListener('keydown', (e) => {
                    if (e.key === 'Enter' && !e.shiftKey) {
                        e.preventDefault();
                        emergencySendMessage();
                    }
                });

                console.log('✅ Emergency WIDDX setup complete');
                showEmergencyNotification('Emergency WIDDX activated! 🚨');
            }
        }

        async function emergencySendMessage() {
            console.log('🚨 Emergency send message');

            const messageInput = document.getElementById('message-input');
            const messagesContainer = document.getElementById('messages-container');

            if (!messageInput || !messagesContainer) return;

            const message = messageInput.value.trim();
            if (!message) return;

            console.log('📤 Emergency sending:', message);

            // Clear input
            messageInput.value = '';

            // Add user message
            addEmergencyMessage(message, 'user');

            // Show thinking
            showEmergencyThinking();

            try {
                const isArabic = /[\u0600-\u06FF]/.test(message);

                const response = await fetch('/api/chat', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Accept': 'application/json'
                    },
                    body: JSON.stringify({
                        message: message,
                        language: isArabic ? 'ar' : 'en',
                        features: {}
                    })
                });

                console.log('📡 Emergency response status:', response.status);

                const result = await response.json();
                console.log('✅ Emergency result:', result);

                hideEmergencyThinking();

                if (result.success) {
                    addEmergencyMessage(result.message, 'assistant');
                    showEmergencyNotification('Message sent successfully! ✅');
                } else {
                    addEmergencyMessage('Error: ' + result.error, 'assistant');
                }

            } catch (error) {
                console.error('❌ Emergency error:', error);
                hideEmergencyThinking();

                const isArabic = /[\u0600-\u06FF]/.test(message);
                const fallback = isArabic
                    ? 'مرحباً! أنا WIDDX AI. عذراً، حدث خطأ في الاتصال.'
                    : 'Hello! I\'m WIDDX AI. Sorry, there was a connection error.';
                addEmergencyMessage(fallback, 'assistant');
            }
        }

        function addEmergencyMessage(content, sender) {
            const messagesContainer = document.getElementById('messages-container');
            if (!messagesContainer) return;

            const messageDiv = document.createElement('div');
            messageDiv.className = 'mb-6 max-w-3xl mx-auto';

            const isUser = sender === 'user';
            const alignClass = isUser ? 'justify-end' : 'justify-start';

            messageDiv.innerHTML = `
                <div class="flex ${alignClass}">
                    <div class="flex items-start space-x-3 max-w-2xl ${isUser ? 'flex-row-reverse space-x-reverse' : ''}">
                        <div class="w-8 h-8 rounded-full flex items-center justify-center flex-shrink-0 ${
                            isUser ? 'bg-gray-300 dark:bg-gray-600' : 'bg-blue-500'
                        }">
                            <span class="text-sm font-bold ${isUser ? 'text-gray-700 dark:text-gray-300' : 'text-white'}">
                                ${isUser ? 'U' : 'W'}
                            </span>
                        </div>
                        <div class="flex-1 min-w-0">
                            <div class="p-4 rounded-lg ${
                                isUser
                                    ? 'bg-blue-500 text-white'
                                    : 'bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700'
                            }">
                                <div class="text-sm ${isUser ? 'text-white' : 'text-gray-900 dark:text-white'}">
                                    ${content}
                                </div>
                            </div>
                            <div class="mt-1 text-xs text-gray-500 dark:text-gray-400 ${isUser ? 'text-right' : 'text-left'}">
                                ${new Date().toLocaleTimeString()} • Emergency Mode
                            </div>
                        </div>
                    </div>
                </div>
            `;

            messagesContainer.appendChild(messageDiv);
            messagesContainer.scrollTop = messagesContainer.scrollHeight;
        }

        function showEmergencyThinking() {
            const thinkingIndicator = document.getElementById('thinking-indicator');
            if (thinkingIndicator) {
                thinkingIndicator.classList.remove('hidden');
            }
        }

        function hideEmergencyThinking() {
            const thinkingIndicator = document.getElementById('thinking-indicator');
            if (thinkingIndicator) {
                thinkingIndicator.classList.add('hidden');
            }
        }

        function showEmergencyNotification(message) {
            const notification = document.createElement('div');
            notification.className = 'fixed top-4 right-4 z-50 p-4 rounded-lg shadow-lg bg-red-500 text-white';
            notification.textContent = message;
            document.body.appendChild(notification);

            setTimeout(() => {
                if (notification.parentElement) {
                    notification.remove();
                }
            }, 3000);
        }
    </script>



    <!-- Script Loading Verification -->
    <script>
        // Verify script loading
        document.addEventListener('DOMContentLoaded', function() {
            console.log('🔧 DOM Content Loaded');

            // Check if main script loaded
            if (typeof WiddxModernInterface !== 'undefined') {
                console.log('✅ WiddxModernInterface class loaded successfully');
            } else {
                console.error('❌ WiddxModernInterface class not found');
            }

            // Check if instance was created
            setTimeout(() => {
                if (window.widdxModern) {
                    console.log('✅ WIDDX Modern instance created successfully');
                } else {
                    console.error('❌ WIDDX Modern instance not found');
                }
            }, 500);
        });
    </script>

    <!-- Enhanced Fallback for debugging -->
    <script>
        // Enhanced fallback system
        setTimeout(() => {
            if (!window.widdxModern) {
                console.error('❌ WIDDX Modern Interface failed to load!');
                console.log('🔧 Attempting enhanced fallback initialization...');

                // Create basic fallback object
                window.widdxModern = {
                    features: {
                        search: false,
                        deepSearch: false,
                        thinkMode: false,
                        imageGeneration: false,
                        vision: false
                    },
                    isTyping: false,

                    toggleFeature: function(feature) {
                        this.features[feature] = !this.features[feature];
                        console.log(`Feature ${feature}: ${this.features[feature] ? 'ON' : 'OFF'}`);

                        // Update UI
                        document.querySelectorAll(`[data-feature="${feature}"]`).forEach(el => {
                            if (this.features[feature]) {
                                el.classList.add('active');
                            } else {
                                el.classList.remove('active');
                            }
                        });

                        // Show notification
                        this.showNotification(`${feature} ${this.features[feature] ? 'activated' : 'deactivated'}`);
                    },

                    showNotification: function(message) {
                        const notification = document.createElement('div');
                        notification.className = 'fixed top-4 right-4 z-50 p-4 bg-blue-500 text-white rounded-lg shadow-lg';
                        notification.textContent = message;
                        document.body.appendChild(notification);
                        setTimeout(() => notification.remove(), 3000);
                    },

                    submitMessage: function() {
                        const input = document.getElementById('message-input');
                        if (input && input.value.trim()) {
                            console.log('📤 Sending message:', input.value);

                            // Add user message to UI
                            this.addMessage(input.value, 'user');

                            // Clear input
                            input.value = '';

                            // Simulate response
                            setTimeout(() => {
                                this.addMessage('Hello! This is a fallback response. The main interface failed to load.', 'assistant');
                            }, 1000);
                        }
                    },

                    addMessage: function(content, sender) {
                        const container = document.getElementById('messages-container');
                        if (container) {
                            const messageDiv = document.createElement('div');
                            messageDiv.className = 'mb-4 p-4 rounded-lg ' + (sender === 'user' ? 'bg-blue-100 ml-8' : 'bg-gray-100 mr-8');
                            messageDiv.innerHTML = `<strong>${sender === 'user' ? 'You' : 'WIDDX'}:</strong> ${content}`;
                            container.appendChild(messageDiv);
                            container.scrollTop = container.scrollHeight;

                            // Hide welcome
                            const welcome = document.getElementById('welcome-message');
                            if (welcome) welcome.style.display = 'none';
                        }
                    }
                };

                // Basic feature toggles
                document.querySelectorAll('.feature-toggle').forEach(toggle => {
                    toggle.addEventListener('click', function(e) {
                        e.preventDefault();
                        const feature = this.dataset.feature;
                        if (feature) {
                            window.widdxModern.toggleFeature(feature);
                        }
                    });
                });

                // Basic form submission
                const chatForm = document.getElementById('chat-form');
                if (chatForm) {
                    chatForm.addEventListener('submit', function(e) {
                        e.preventDefault();
                        window.widdxModern.submitMessage();
                    });
                }

                // Enter key handler
                const messageInput = document.getElementById('message-input');
                if (messageInput) {
                    messageInput.addEventListener('keydown', function(e) {
                        if (e.key === 'Enter' && !e.shiftKey) {
                            e.preventDefault();
                            window.widdxModern.submitMessage();
                        }
                    });
                }

                console.log('✅ Fallback system initialized');
                window.widdxModern.showNotification('Fallback mode active - basic functionality available');

            } else {
                console.log('✅ WIDDX Modern Interface loaded successfully!');
            }
        }, 1500);
    </script>
</body>
</html>
<?php /**PATH C:\Users\<USER>\Desktop\widdx-ai\resources\views/widdx-modern.blade.php ENDPATH**/ ?>