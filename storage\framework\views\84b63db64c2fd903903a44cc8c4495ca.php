<!DOCTYPE html>
<html lang="en" class="h-full">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="csrf-token" content="<?php echo e(csrf_token()); ?>">
    <title>WIDDX AI - Modern Chat Interface</title>

    <!-- Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">

    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.2/css/all.min.css">

    <!-- Tai<PERSON>wind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>

    <!-- Advanced Styles -->
    <link rel="stylesheet" href="<?php echo e(asset('css/widdx-advanced.css')); ?>">

    <script>
        tailwind.config = {
            darkMode: 'class',
            theme: {
                extend: {
                    fontFamily: {
                        'sans': ['Inter', 'system-ui', 'sans-serif'],
                    },
                    colors: {
                        'widdx': {
                            50: '#fff7ed',
                            100: '#ffedd5',
                            200: '#fed7aa',
                            300: '#fdba74',
                            400: '#fb923c',
                            500: '#f97316',
                            600: '#ea580c',
                            700: '#c2410c',
                            800: '#9a3412',
                            900: '#7c2d12',
                        }
                    },
                    animation: {
                        'fade-in': 'fadeIn 0.5s ease-in-out',
                        'slide-up': 'slideUp 0.3s ease-out',
                        'slide-down': 'slideDown 0.3s ease-out',
                        'scale-in': 'scaleIn 0.2s ease-out',
                        'bounce-soft': 'bounceSoft 2s infinite',
                        'pulse-slow': 'pulse 3s infinite',
                        'float': 'float 3s ease-in-out infinite',
                        'glow': 'glow 2s ease-in-out infinite alternate',
                        'typing': 'typing 1.5s steps(20) infinite',
                        'wiggle': 'wiggle 1s ease-in-out infinite',
                        'shake': 'shake 0.5s ease-in-out'
                    },
                    keyframes: {
                        fadeIn: {
                            '0%': { opacity: '0', transform: 'translateY(10px)' },
                            '100%': { opacity: '1', transform: 'translateY(0)' }
                        },
                        slideUp: {
                            '0%': { transform: 'translateY(100%)', opacity: '0' },
                            '100%': { transform: 'translateY(0)', opacity: '1' }
                        },
                        slideDown: {
                            '0%': { transform: 'translateY(-100%)', opacity: '0' },
                            '100%': { transform: 'translateY(0)', opacity: '1' }
                        },
                        scaleIn: {
                            '0%': { transform: 'scale(0.9)', opacity: '0' },
                            '100%': { transform: 'scale(1)', opacity: '1' }
                        },
                        bounceSoft: {
                            '0%, 100%': { transform: 'translateY(0)' },
                            '50%': { transform: 'translateY(-5px)' }
                        },
                        float: {
                            '0%, 100%': { transform: 'translateY(0px)' },
                            '50%': { transform: 'translateY(-10px)' }
                        },
                        glow: {
                            '0%': { boxShadow: '0 0 5px rgba(249, 115, 22, 0.5)' },
                            '100%': { boxShadow: '0 0 20px rgba(249, 115, 22, 0.8)' }
                        },
                        typing: {
                            '0%': { width: '0' },
                            '50%': { width: '100%' },
                            '100%': { width: '0' }
                        },
                        wiggle: {
                            '0%, 100%': { transform: 'rotate(-3deg)' },
                            '50%': { transform: 'rotate(3deg)' }
                        },
                        shake: {
                            '0%, 100%': { transform: 'translateX(0)' },
                            '25%': { transform: 'translateX(-5px)' },
                            '75%': { transform: 'translateX(5px)' }
                        }
                    }
                }
            }
        }
    </script>

    <style>
        /* Custom scrollbar */
        .custom-scrollbar::-webkit-scrollbar {
            width: 6px;
        }
        .custom-scrollbar::-webkit-scrollbar-track {
            background: transparent;
        }
        .custom-scrollbar::-webkit-scrollbar-thumb {
            background: rgba(156, 163, 175, 0.5);
            border-radius: 3px;
        }
        .custom-scrollbar::-webkit-scrollbar-thumb:hover {
            background: rgba(156, 163, 175, 0.8);
        }

        /* Interactive hover effects */
        .hover-lift {
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        }
        .hover-lift:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
        }

        .hover-glow {
            transition: all 0.3s ease;
        }
        .hover-glow:hover {
            box-shadow: 0 0 20px rgba(249, 115, 22, 0.4);
            transform: scale(1.02);
        }

        /* Gradient backgrounds */
        .gradient-bg {
            background: linear-gradient(135deg, #f97316 0%, #ea580c 100%);
        }
        .gradient-bg-soft {
            background: linear-gradient(135deg, rgba(249, 115, 22, 0.1) 0%, rgba(234, 88, 12, 0.1) 100%);
        }

        /* Floating particles effect */
        .particles {
            position: absolute;
            width: 100%;
            height: 100%;
            overflow: hidden;
            pointer-events: none;
        }
        .particle {
            position: absolute;
            background: rgba(249, 115, 22, 0.3);
            border-radius: 50%;
            animation: float 6s ease-in-out infinite;
        }
        .particle:nth-child(1) { width: 4px; height: 4px; left: 10%; animation-delay: 0s; }
        .particle:nth-child(2) { width: 6px; height: 6px; left: 20%; animation-delay: 1s; }
        .particle:nth-child(3) { width: 3px; height: 3px; left: 30%; animation-delay: 2s; }
        .particle:nth-child(4) { width: 5px; height: 5px; left: 40%; animation-delay: 3s; }
        .particle:nth-child(5) { width: 4px; height: 4px; left: 50%; animation-delay: 4s; }
        .particle:nth-child(6) { width: 6px; height: 6px; left: 60%; animation-delay: 5s; }
        .particle:nth-child(7) { width: 3px; height: 3px; left: 70%; animation-delay: 0.5s; }
        .particle:nth-child(8) { width: 5px; height: 5px; left: 80%; animation-delay: 1.5s; }
        .particle:nth-child(9) { width: 4px; height: 4px; left: 90%; animation-delay: 2.5s; }

        /* Typing indicator */
        .typing-dots {
            display: inline-flex;
            align-items: center;
        }
        .typing-dots span {
            height: 8px;
            width: 8px;
            background: currentColor;
            border-radius: 50%;
            display: inline-block;
            margin: 0 2px;
            animation: typing-bounce 1.4s infinite ease-in-out both;
        }
        .typing-dots span:nth-child(1) { animation-delay: -0.32s; }
        .typing-dots span:nth-child(2) { animation-delay: -0.16s; }

        @keyframes typing-bounce {
            0%, 80%, 100% { transform: scale(0.8); opacity: 0.5; }
            40% { transform: scale(1); opacity: 1; }
        }

        /* Smooth transitions for all interactive elements */
        button, .feature-toggle, .tooltip, input, textarea {
            transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
        }

        /* Glass morphism effect */
        .glass {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }
        .dark .glass {
            background: rgba(0, 0, 0, 0.2);
            border: 1px solid rgba(255, 255, 255, 0.1);
        }

        /* Pulse effect for active elements */
        .pulse-ring {
            position: relative;
        }
        .pulse-ring::before {
            content: '';
            position: absolute;
            top: 50%;
            left: 50%;
            width: 100%;
            height: 100%;
            border-radius: inherit;
            background: rgba(249, 115, 22, 0.3);
            transform: translate(-50%, -50%) scale(1);
            animation: pulse-ring 2s infinite;
        }

        @keyframes pulse-ring {
            0% { transform: translate(-50%, -50%) scale(1); opacity: 1; }
            100% { transform: translate(-50%, -50%) scale(1.5); opacity: 0; }
        }

        @keyframes fadeOut {
            0% { opacity: 1; transform: translateY(0); }
            100% { opacity: 0; transform: translateY(-10px); }
        }

        /* Advanced Animations */
        @keyframes slideIn {
            from { opacity: 0; transform: translateY(10px); }
            to { opacity: 1; transform: translateY(0); }
        }

        @keyframes fadeInScale {
            from { opacity: 0; transform: scale(0.95); }
            to { opacity: 1; transform: scale(1); }
        }

        @keyframes pulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.5; }
        }

        @keyframes glow {
            0%, 100% { box-shadow: 0 0 20px rgba(249, 115, 22, 0.3); }
            50% { box-shadow: 0 0 30px rgba(249, 115, 22, 0.6); }
        }

        @keyframes typing {
            0%, 100% { transform: translateY(0); }
            50% { transform: translateY(-5px); }
        }

        .animate-slide-in {
            animation: slideIn 0.3s ease-out;
        }

        .animate-fade-in-scale {
            animation: fadeInScale 0.4s ease-out;
        }

        .animate-glow {
            animation: glow 2s ease-in-out infinite;
        }

        .animate-typing {
            animation: typing 1.4s ease-in-out infinite;
        }

        /* Feature toggle styles */
        .feature-toggle {
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            position: relative;
            overflow: hidden;
        }

        .feature-toggle::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
            transition: left 0.5s ease;
        }

        .feature-toggle:hover::before {
            left: 100%;
        }

        .feature-toggle.active {
            background: linear-gradient(135deg, #f97316, #ea580c);
            color: white;
            box-shadow: 0 4px 20px rgba(249, 115, 22, 0.4);
            transform: translateY(-2px);
        }

        .feature-toggle.active::after {
            content: '✓';
            position: absolute;
            top: 2px;
            right: 6px;
            font-size: 10px;
            font-weight: bold;
            color: white;
            background: rgba(255, 255, 255, 0.2);
            border-radius: 50%;
            width: 16px;
            height: 16px;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .feature-toggle:hover {
            transform: translateY(-1px);
            box-shadow: 0 6px 20px rgba(0, 0, 0, 0.15);
        }

        /* Message bubbles */
        .message-bubble {
            transition: all 0.3s ease;
        }

        .message-bubble:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
        }

        /* Glassmorphism effect */
        .glass {
            backdrop-filter: blur(10px);
            background: rgba(255, 255, 255, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .glass-dark {
            backdrop-filter: blur(10px);
            background: rgba(0, 0, 0, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.1);
        }

        /* Floating action button */
        .fab {
            position: fixed;
            bottom: 100px;
            right: 20px;
            width: 56px;
            height: 56px;
            border-radius: 50%;
            background: linear-gradient(135deg, #f97316, #ea580c);
            color: white;
            border: none;
            box-shadow: 0 4px 20px rgba(249, 115, 22, 0.4);
            cursor: pointer;
            transition: all 0.3s ease;
            z-index: 1000;
        }

        .fab:hover {
            transform: scale(1.1);
            box-shadow: 0 6px 25px rgba(249, 115, 22, 0.6);
        }

        /* Voice recording animation */
        .recording {
            animation: pulse 1s ease-in-out infinite;
            background: #ef4444 !important;
        }

        /* Code syntax highlighting */
        .code-block {
            background: #1e293b;
            color: #e2e8f0;
            border-radius: 8px;
            padding: 16px;
            font-family: 'JetBrains Mono', monospace;
            overflow-x: auto;
        }

        /* Progress bar */
        .progress-bar {
            height: 3px;
            background: linear-gradient(90deg, #f97316, #ea580c);
            border-radius: 2px;
            transition: width 0.3s ease;
        }

        /* Tooltip */
        .tooltip {
            position: relative;
        }

        .tooltip::after {
            content: attr(data-tooltip);
            position: absolute;
            bottom: 100%;
            left: 50%;
            transform: translateX(-50%);
            background: rgba(0, 0, 0, 0.8);
            color: white;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            white-space: nowrap;
            opacity: 0;
            pointer-events: none;
            transition: opacity 0.3s ease;
            z-index: 1000;
        }

        .tooltip:hover::after {
            opacity: 1;
        }
    </style>
</head>
<body class="h-full font-sans bg-gray-50 dark:bg-gray-900">
    <div class="flex h-full">
        <!-- Sidebar -->
        <div class="hidden lg:flex lg:w-80 lg:flex-col">
            <div class="flex flex-col flex-grow bg-white/80 backdrop-blur-lg border-r border-gray-200 dark:bg-gray-800/80 dark:border-gray-700 relative">
                <!-- Floating particles background -->
                <div class="particles">
                    <div class="particle"></div>
                    <div class="particle"></div>
                    <div class="particle"></div>
                    <div class="particle"></div>
                    <div class="particle"></div>
                    <div class="particle"></div>
                    <div class="particle"></div>
                    <div class="particle"></div>
                    <div class="particle"></div>
                </div>

                <!-- Logo -->
                <div class="flex items-center px-6 py-4 border-b border-gray-200 dark:border-gray-700 relative z-10">
                    <div class="flex items-center space-x-3 animate-fade-in">
                        <div class="flex items-center justify-center w-8 h-8 rounded-lg bg-gradient-to-br from-widdx-500 to-widdx-600 hover-glow pulse-ring">
                            <span class="text-lg font-bold text-white animate-bounce-soft">W</span>
                        </div>
                        <div>
                            <h1 class="text-xl font-bold text-gray-900 dark:text-white hover:text-widdx-500 transition-colors cursor-default">WIDDX AI</h1>
                            <p class="text-sm text-gray-500 dark:text-gray-400 animate-pulse-slow">Intelligent Assistant</p>
                        </div>
                    </div>
                </div>

                <!-- New Chat Button -->
                <div class="p-4">
                    <button id="new-chat-btn" class="flex items-center justify-center w-full px-4 py-3 text-white transition-colors duration-200 rounded-lg bg-widdx-500 hover:bg-widdx-600">
                        <i class="mr-2 fas fa-plus"></i>
                        New Chat
                    </button>
                </div>

                <!-- Feature Toggles -->
                <div class="px-4 pb-4">
                    <div class="flex items-center justify-between mb-3">
                        <h3 class="text-sm font-medium text-gray-700 dark:text-gray-300">AI Features</h3>
                        <button id="reset-features" class="text-xs text-gray-500 transition-colors hover:text-widdx-500" title="Reset all features">
                            <i class="fas fa-undo"></i>
                        </button>
                    </div>
                    <!-- Manual Control Features -->
                    <div class="space-y-2">
                        <button class="flex items-center w-full px-3 py-2 text-sm text-gray-700 bg-gray-100 rounded-lg feature-toggle tooltip dark:bg-gray-700 dark:text-gray-300 hover-lift hover-glow animate-fade-in" data-feature="thinkMode" data-tooltip="Deep step-by-step reasoning and analysis" style="animation-delay: 0.1s">
                            <i class="w-4 mr-3 fas fa-brain animate-pulse-slow"></i>
                            <span class="flex-1 text-left">Think Mode</span>
                            <span class="text-xs opacity-60 animate-bounce-soft">Manual</span>
                        </button>
                        <button class="flex items-center w-full px-3 py-2 text-sm text-gray-700 bg-gray-100 rounded-lg feature-toggle tooltip dark:bg-gray-700 dark:text-gray-300 hover-lift hover-glow animate-fade-in" data-feature="deepSearch" data-tooltip="Advanced AI-powered search" style="animation-delay: 0.2s">
                            <i class="w-4 mr-3 fas fa-search-plus animate-pulse-slow"></i>
                            <span class="flex-1 text-left">Deep Search</span>
                            <span class="text-xs opacity-60 animate-bounce-soft">Manual</span>
                        </button>
                        <button class="flex items-center w-full px-3 py-2 text-sm text-gray-700 bg-gray-100 rounded-lg feature-toggle tooltip dark:bg-gray-700 dark:text-gray-300 hover-lift hover-glow animate-fade-in" data-feature="ultraDeepSearch" data-tooltip="Ultra deep comprehensive search" style="animation-delay: 0.3s">
                            <i class="w-4 mr-3 fas fa-search-location animate-pulse-slow"></i>
                            <span class="flex-1 text-left">Ultra Deep Search</span>
                            <span class="text-xs opacity-60 animate-bounce-soft">Manual</span>
                        </button>
                    </div>

                    <!-- Auto Features Info -->
                    <div class="p-3 mt-4 border border-blue-200 rounded-lg bg-blue-50 dark:bg-blue-900/20 dark:border-blue-800">
                        <div class="flex items-center mb-2">
                            <i class="mr-2 text-blue-500 fas fa-magic"></i>
                            <span class="text-sm font-medium text-blue-700 dark:text-blue-300">Auto Features</span>
                        </div>
                        <div class="space-y-1 text-xs text-blue-600 dark:text-blue-400">
                            <div>• <strong>Search:</strong> Auto-activated for current info</div>
                            <div>• <strong>Images:</strong> Auto-activated for drawing requests</div>
                            <div>• <strong>Translation:</strong> Auto-activated for translation</div>
                            <div>• <strong>Programming:</strong> Auto-activated for code</div>
                            <div>• <strong>Vision:</strong> Auto-activated for image analysis</div>
                        </div>
                    </div>

                    <!-- Usage Tips -->
                    <div class="p-3 mt-3 border border-green-200 rounded-lg bg-green-50 dark:bg-green-900/20 dark:border-green-800">
                        <div class="flex items-center mb-2">
                            <i class="mr-2 text-green-500 fas fa-lightbulb"></i>
                            <span class="text-sm font-medium text-green-700 dark:text-green-300">Usage Tips</span>
                        </div>
                        <div class="space-y-1 text-xs text-green-600 dark:text-green-400">
                            <div>• <strong>Think Mode:</strong> For complex analysis and reasoning</div>
                            <div>• <strong>Deep Search:</strong> For comprehensive research</div>
                            <div>• <strong>Ultra Deep:</strong> For academic-level research</div>
                        </div>
                    </div>

                    <!-- Feature Statistics -->
                    <div class="p-3 mt-4 rounded-lg bg-gray-50 dark:bg-gray-700">
                        <div class="flex items-center justify-between text-xs text-gray-600 dark:text-gray-400">
                            <span>Active Features</span>
                            <span id="active-count" class="font-medium">0/5</span>
                        </div>
                        <div class="h-1 mt-2 bg-gray-200 rounded-full dark:bg-gray-600">
                            <div id="features-progress" class="h-1 rounded-full progress-bar" style="width: 0%"></div>
                        </div>
                    </div>
                </div>

                <!-- Chat History -->
                <div class="flex-1 px-4 pb-4">
                    <div class="flex items-center justify-between mb-3">
                        <h3 class="text-sm font-medium text-gray-700 dark:text-gray-300">Recent Chats</h3>
                        <div class="flex items-center space-x-2">
                            <button id="search-chats" class="text-xs text-gray-500 transition-colors hover:text-widdx-500" title="Search chats">
                                <i class="fas fa-search"></i>
                            </button>
                            <button id="clear-history" class="text-xs text-gray-500 transition-colors hover:text-red-500" title="Clear history">
                                <i class="fas fa-trash"></i>
                            </button>
                        </div>
                    </div>

                    <!-- Search Input -->
                    <div id="chat-search-container" class="hidden mb-3">
                        <input
                            type="text"
                            id="chat-search-input"
                            placeholder="Search conversations..."
                            class="w-full px-3 py-2 text-sm bg-white border border-gray-300 rounded-lg dark:bg-gray-700 dark:border-gray-600 focus:ring-2 focus:ring-widdx-500 focus:border-transparent"
                        >
                    </div>

                    <div id="chat-history-list" class="space-y-1 overflow-y-auto custom-scrollbar max-h-64">
                        <!-- Chat history items will be populated here -->
                        <div id="no-chats" class="py-8 text-sm text-center text-gray-500 dark:text-gray-400">
                            <i class="mb-2 text-2xl opacity-50 fas fa-comments"></i>
                            <div>No previous chats</div>
                            <div class="mt-1 text-xs">Start a conversation to see history</div>
                        </div>
                    </div>

                    <!-- Chat Statistics -->
                    <div class="p-3 mt-4 rounded-lg bg-gray-50 dark:bg-gray-700">
                        <div class="space-y-1 text-xs text-gray-600 dark:text-gray-400">
                            <div class="flex justify-between">
                                <span>Total Chats</span>
                                <span id="total-chats">0</span>
                            </div>
                            <div class="flex justify-between">
                                <span>Today</span>
                                <span id="today-chats">0</span>
                            </div>
                            <div class="flex justify-between">
                                <span>This Week</span>
                                <span id="week-chats">0</span>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Settings -->
                <div class="p-4 border-t border-gray-200 dark:border-gray-700">
                    <!-- Language and Theme -->
                    <div class="flex items-center justify-between mb-4">
                        <select id="language-select" class="text-sm text-gray-700 bg-transparent border-none cursor-pointer dark:text-gray-300 focus:ring-0">
                            <option value="en">🇺🇸 English</option>
                            <option value="ar">🇸🇦 العربية</option>
                            <option value="es">🇪🇸 Español</option>
                            <option value="fr">🇫🇷 Français</option>
                            <option value="de">🇩🇪 Deutsch</option>
                            <option value="zh">🇨🇳 中文</option>
                            <option value="ja">🇯🇵 日本語</option>
                            <option value="ko">🇰🇷 한국어</option>
                            <option value="ru">🇷🇺 Русский</option>
                        </select>
                        <div class="flex items-center space-x-2">
                            <a href="/help" class="p-2 text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200 tooltip" data-tooltip="Help & Documentation">
                                <i class="fas fa-question-circle"></i>
                            </a>
                            <a href="/about" class="p-2 text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200 tooltip" data-tooltip="About WIDDX AI">
                                <i class="fas fa-info-circle"></i>
                            </a>
                            <a href="/settings" class="p-2 text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200 tooltip" data-tooltip="Settings">
                                <i class="fas fa-cog"></i>
                            </a>
                            <button id="theme-toggle" class="p-2 text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200 tooltip" data-tooltip="Toggle theme">
                                <i class="fas fa-moon dark:hidden"></i>
                                <i class="hidden fas fa-sun dark:inline"></i>
                            </button>
                        </div>
                    </div>

                    <!-- Advanced Settings Panel -->
                    <div id="advanced-settings" class="hidden space-y-3">
                        <!-- Voice Settings -->
                        <div class="p-3 rounded-lg bg-gray-50 dark:bg-gray-700">
                            <h4 class="mb-2 text-xs font-medium text-gray-700 dark:text-gray-300">Voice & Audio</h4>
                            <div class="space-y-2">
                                <label class="flex items-center justify-between text-xs">
                                    <span class="text-gray-600 dark:text-gray-400">Enable Voice Input</span>
                                    <input type="checkbox" id="voice-input-toggle" class="border-gray-300 rounded text-widdx-500 focus:ring-widdx-500">
                                </label>
                                <label class="flex items-center justify-between text-xs">
                                    <span class="text-gray-600 dark:text-gray-400">Sound Effects</span>
                                    <input type="checkbox" id="sound-effects-toggle" class="border-gray-300 rounded text-widdx-500 focus:ring-widdx-500">
                                </label>
                            </div>
                        </div>

                        <!-- Display Settings -->
                        <div class="p-3 rounded-lg bg-gray-50 dark:bg-gray-700">
                            <h4 class="mb-2 text-xs font-medium text-gray-700 dark:text-gray-300">Display</h4>
                            <div class="space-y-2">
                                <label class="flex items-center justify-between text-xs">
                                    <span class="text-gray-600 dark:text-gray-400">Animations</span>
                                    <input type="checkbox" id="animations-toggle" checked class="border-gray-300 rounded text-widdx-500 focus:ring-widdx-500">
                                </label>
                                <label class="flex items-center justify-between text-xs">
                                    <span class="text-gray-600 dark:text-gray-400">Compact Mode</span>
                                    <input type="checkbox" id="compact-mode-toggle" class="border-gray-300 rounded text-widdx-500 focus:ring-widdx-500">
                                </label>
                            </div>
                        </div>

                        <!-- Privacy Settings -->
                        <div class="p-3 rounded-lg bg-gray-50 dark:bg-gray-700">
                            <h4 class="mb-2 text-xs font-medium text-gray-700 dark:text-gray-300">Privacy</h4>
                            <div class="space-y-2">
                                <label class="flex items-center justify-between text-xs">
                                    <span class="text-gray-600 dark:text-gray-400">Save Chat History</span>
                                    <input type="checkbox" id="save-history-toggle" checked class="border-gray-300 rounded text-widdx-500 focus:ring-widdx-500">
                                </label>
                                <label class="flex items-center justify-between text-xs">
                                    <span class="text-gray-600 dark:text-gray-400">Analytics</span>
                                    <input type="checkbox" id="analytics-toggle" class="border-gray-300 rounded text-widdx-500 focus:ring-widdx-500">
                                </label>
                            </div>
                        </div>
                    </div>

                    <!-- User Info -->
                    <div class="p-3 mt-4 rounded-lg bg-gradient-to-r from-widdx-50 to-orange-50 dark:from-widdx-900/20 dark:to-orange-900/20">
                        <div class="flex items-center space-x-3">
                            <div class="flex items-center justify-center w-8 h-8 rounded-full bg-gradient-to-br from-widdx-500 to-widdx-600">
                                <span class="text-sm font-bold text-white">U</span>
                            </div>
                            <div class="flex-1 min-w-0">
                                <div class="text-sm font-medium text-gray-900 truncate dark:text-white">Guest User</div>
                                <div class="text-xs text-gray-500 dark:text-gray-400">Free Plan</div>
                            </div>
                            <button class="text-xs text-widdx-600 dark:text-widdx-400 hover:text-widdx-700 dark:hover:text-widdx-300">
                                <i class="fas fa-crown"></i>
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Main Content -->
        <div class="flex flex-col flex-1 min-w-0">
            <!-- Header -->
            <header class="px-4 py-3 bg-white/80 backdrop-blur-lg border-b border-gray-200 dark:bg-gray-800/80 dark:border-gray-700 animate-slide-down">
                <div class="flex items-center justify-between">
                    <div class="flex items-center space-x-4">
                        <button class="p-2 text-gray-500 lg:hidden hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200 hover-lift">
                            <i class="fas fa-bars"></i>
                        </button>
                        <div class="animate-fade-in">
                            <h2 class="text-lg font-semibold text-gray-900 dark:text-white hover:text-widdx-500 transition-colors cursor-default">New Conversation</h2>
                            <div class="flex items-center space-x-2 text-sm text-gray-500 dark:text-gray-400">
                                <span class="w-2 h-2 bg-green-500 rounded-full animate-pulse"></span>
                                <span class="animate-pulse-slow">Ready</span>
                                <span id="active-features" class="hidden animate-fade-in">• Features: <span id="features-list"></span></span>
                            </div>
                        </div>
                    </div>

                    <!-- Quick Manual Feature Toggles -->
                    <div class="items-center hidden space-x-2 md:flex">
                        <button class="feature-toggle px-3 py-1.5 text-xs bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 rounded-full" data-feature="thinkMode">
                            <i class="mr-1 fas fa-brain"></i>Think
                        </button>
                        <button class="feature-toggle px-3 py-1.5 text-xs bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 rounded-full" data-feature="deepSearch">
                            <i class="mr-1 fas fa-search-plus"></i>Deep Search
                        </button>
                        <button class="feature-toggle px-3 py-1.5 text-xs bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 rounded-full" data-feature="ultraDeepSearch">
                            <i class="mr-1 fas fa-search-location"></i>Ultra Deep
                        </button>

                        <?php if(config('app.debug')): ?>
                        <!-- Test Button (Development Only) -->
                        <button id="run-tests" class="px-3 py-1.5 text-xs bg-red-100 dark:bg-red-900 text-red-700 dark:text-red-300 rounded-full hover:bg-red-200 dark:hover:bg-red-800 transition-colors" title="Run Interface Tests">
                            <i class="mr-1 fas fa-bug"></i>Test
                        </button>
                        <?php endif; ?>
                    </div>
                </div>
            </header>

            <!-- Chat Area -->
            <main class="flex-1 overflow-hidden">
                <div class="flex flex-col h-full">
                    <!-- Messages -->
                    <div id="messages-container" class="flex-1 px-4 py-6 overflow-y-auto custom-scrollbar">
                        <!-- Welcome Message -->
                        <div id="welcome-message" class="max-w-3xl py-12 mx-auto text-center">
                            <div class="flex items-center justify-center w-16 h-16 mx-auto mb-6 bg-gradient-to-br from-widdx-500 to-widdx-600 rounded-2xl">
                                <span class="text-2xl font-bold text-white">W</span>
                            </div>
                            <h1 class="mb-4 text-3xl font-bold text-gray-900 dark:text-white">Welcome to WIDDX AI</h1>
                            <p class="mb-8 text-lg text-gray-600 dark:text-gray-400">
                                Your intelligent assistant with advanced AI capabilities.
                                Activate features manually and start chatting!
                            </p>

                            <div class="grid max-w-4xl grid-cols-1 gap-4 mx-auto md:grid-cols-2 lg:grid-cols-3">
                                <div class="p-6 transition-colors bg-white border border-gray-200 cursor-pointer feature-card dark:bg-gray-800 rounded-xl dark:border-gray-700 hover:border-widdx-300 dark:hover:border-widdx-600" data-feature="search">
                                    <div class="flex items-center justify-center w-12 h-12 mb-4 bg-blue-100 rounded-lg dark:bg-blue-900">
                                        <i class="text-xl text-blue-600 fas fa-search dark:text-blue-400"></i>
                                    </div>
                                    <h3 class="mb-2 font-semibold text-gray-900 dark:text-white">Web Search</h3>
                                    <p class="text-sm text-gray-600 dark:text-gray-400">Real-time internet search for latest information</p>
                                </div>

                                <div class="p-6 transition-colors bg-white border border-gray-200 cursor-pointer feature-card dark:bg-gray-800 rounded-xl dark:border-gray-700 hover:border-widdx-300 dark:hover:border-widdx-600" data-feature="thinkMode">
                                    <div class="flex items-center justify-center w-12 h-12 mb-4 bg-purple-100 rounded-lg dark:bg-purple-900">
                                        <i class="text-xl text-purple-600 fas fa-brain dark:text-purple-400"></i>
                                    </div>
                                    <h3 class="mb-2 font-semibold text-gray-900 dark:text-white">Think Mode</h3>
                                    <p class="text-sm text-gray-600 dark:text-gray-400">Step-by-step reasoning and analysis</p>
                                </div>

                                <div class="p-6 transition-colors bg-white border border-gray-200 cursor-pointer feature-card dark:bg-gray-800 rounded-xl dark:border-gray-700 hover:border-widdx-300 dark:hover:border-widdx-600" data-feature="imageGeneration">
                                    <div class="flex items-center justify-center w-12 h-12 mb-4 bg-green-100 rounded-lg dark:bg-green-900">
                                        <i class="text-xl text-green-600 fas fa-image dark:text-green-400"></i>
                                    </div>
                                    <h3 class="mb-2 font-semibold text-gray-900 dark:text-white">Image Generation</h3>
                                    <p class="text-sm text-gray-600 dark:text-gray-400">Create images from text descriptions</p>
                                </div>
                            </div>

                            <div class="p-4 mt-8 rounded-lg bg-widdx-50 dark:bg-widdx-900/20">
                                <h4 class="mb-2 font-medium text-widdx-800 dark:text-widdx-200">💡 Quick Tips</h4>
                                <ul class="space-y-1 text-sm text-widdx-700 dark:text-widdx-300">
                                    <li>• Features are OFF by default - click to activate</li>
                                    <li>• Use Alt+1-5 for quick feature toggles</li>
                                    <li>• Chat in any language, I'll respond accordingly</li>
                                </ul>
                            </div>
                        </div>

                        <!-- Chat messages will be inserted here -->
                    </div>

                    <!-- Thinking Indicator -->
                    <div id="thinking-indicator" class="hidden px-4 py-2 animate-slide-up">
                        <div class="max-w-3xl mx-auto">
                            <div class="flex items-center p-4 space-x-3 bg-gray-100/80 dark:bg-gray-800/80 backdrop-blur-lg rounded-lg glass hover-lift">
                                <div class="flex items-center justify-center w-8 h-8 rounded-full bg-gradient-to-br from-widdx-500 to-widdx-600 hover-glow pulse-ring">
                                    <span class="text-sm font-bold text-white animate-bounce-soft">W</span>
                                </div>
                                <div class="flex-1">
                                    <div class="flex items-center space-x-2">
                                        <span class="text-sm text-gray-600 dark:text-gray-400 animate-pulse-slow">WIDDX is thinking</span>
                                        <div class="typing-dots">
                                            <span></span>
                                            <span></span>
                                            <span></span>
                                        </div>
                                    </div>
                                    <div class="mt-1 text-xs text-gray-500 dark:text-gray-500 animate-fade-in">
                                        Generating intelligent response...
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Input Area -->
                    <div class="p-4 bg-white/80 backdrop-blur-lg border-t border-gray-200 dark:border-gray-700 dark:bg-gray-800/80 animate-slide-up">
                        <div class="max-w-3xl mx-auto">
                            <!-- Active Features Display -->
                            <div id="active-features-bar" class="hidden mb-3 animate-fade-in">
                                <div class="flex items-center p-2 space-x-2 rounded-lg bg-widdx-50/80 dark:bg-widdx-900/20 glass hover-lift">
                                    <i class="text-sm fas fa-magic text-widdx-500 animate-pulse-slow"></i>
                                    <span class="text-sm text-widdx-700 dark:text-widdx-300 animate-bounce-soft">Active:</span>
                                    <div id="active-features-list" class="flex flex-wrap gap-1"></div>
                                    <button id="clear-features" class="ml-auto text-xs text-widdx-600 hover:text-widdx-800 dark:text-widdx-400 dark:hover:text-widdx-200 hover-lift">
                                        <i class="fas fa-times"></i>
                                    </button>
                                </div>
                            </div>

                            <form id="chat-form" class="relative">
                                <div class="flex items-end space-x-3">
                                    <div class="relative flex-1">
                                        <!-- Input Actions Bar -->
                                        <div class="flex items-center justify-between mb-2 animate-fade-in">
                                            <div class="flex items-center space-x-2">
                                                <button type="button" id="attach-file" class="p-1.5 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 tooltip hover-lift" data-tooltip="Attach file">
                                                    <i class="text-sm fas fa-paperclip animate-pulse-slow"></i>
                                                </button>
                                                <button type="button" id="voice-input" class="p-1.5 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 tooltip hover-lift" data-tooltip="Voice input">
                                                    <i class="text-sm fas fa-microphone animate-pulse-slow"></i>
                                                </button>
                                                <button type="button" id="emoji-picker" class="p-1.5 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 tooltip hover-lift" data-tooltip="Add emoji">
                                                    <i class="text-sm fas fa-smile animate-pulse-slow"></i>
                                                </button>
                                            </div>
                                            <div class="flex items-center space-x-2 text-xs text-gray-500 dark:text-gray-400 animate-bounce-soft">
                                                <span id="char-count">0</span>
                                                <span>/</span>
                                                <span>4000</span>
                                            </div>
                                        </div>

                                        <div class="relative">
                                            <textarea
                                                id="message-input"
                                                rows="1"
                                                placeholder="Ask WIDDX anything... (Ctrl+Enter to send)"
                                                class="w-full px-4 py-3 pr-20 text-gray-900 placeholder-gray-500 transition-all duration-200 bg-white/80 backdrop-blur-lg border border-gray-300 rounded-lg resize-none dark:border-gray-600 dark:bg-gray-700/80 dark:text-white dark:placeholder-gray-400 focus:ring-2 focus:ring-widdx-500 focus:border-transparent hover-lift glass"
                                                style="max-height: 120px;"
                                                maxlength="4000"
                                            ></textarea>

                                            <!-- Input Actions -->
                                            <div class="absolute flex items-center space-x-1 right-2 bottom-2">
                                                <button type="button" id="clear-input" class="items-center justify-center hidden w-6 h-6 text-gray-400 rounded hover:text-gray-600 dark:hover:text-gray-300 tooltip hover-lift" data-tooltip="Clear">
                                                    <i class="text-xs fas fa-times animate-pulse-slow"></i>
                                                </button>
                                                <button type="submit" id="send-button" disabled class="flex items-center justify-center w-8 h-8 text-white transition-all duration-200 rounded-lg bg-widdx-500 hover:bg-widdx-600 disabled:bg-gray-300 dark:disabled:bg-gray-600 tooltip hover-glow pulse-ring" data-tooltip="Send message">
                                                    <i class="text-sm fas fa-paper-plane animate-bounce-soft"></i>
                                                </button>
                                            </div>
                                        </div>

                                        <!-- Suggestions -->
                                        <div id="suggestions" class="hidden mt-2">
                                            <div class="flex flex-wrap gap-2">
                                                <button type="button" class="px-3 py-1 text-xs text-gray-700 transition-colors bg-gray-100 rounded-full suggestion-btn dark:bg-gray-700 dark:text-gray-300 hover:bg-gray-200 dark:hover:bg-gray-600">
                                                    "What can you do?"
                                                </button>
                                                <button type="button" class="px-3 py-1 text-xs text-gray-700 transition-colors bg-gray-100 rounded-full suggestion-btn dark:bg-gray-700 dark:text-gray-300 hover:bg-gray-200 dark:hover:bg-gray-600">
                                                    "Help me with coding"
                                                </button>
                                                <button type="button" class="px-3 py-1 text-xs text-gray-700 transition-colors bg-gray-100 rounded-full suggestion-btn dark:bg-gray-700 dark:text-gray-300 hover:bg-gray-200 dark:hover:bg-gray-600">
                                                    "Generate an image"
                                                </button>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
            </main>
        </div>
    </div>

    <!-- Floating Action Button -->
    <button id="voice-fab" class="hidden fab" title="Voice Input">
        <i class="fas fa-microphone"></i>
    </button>

    <!-- File Upload Modal -->
    <div id="file-upload-modal" class="fixed inset-0 z-50 items-center justify-center hidden bg-black bg-opacity-50">
        <div class="w-full max-w-md p-6 mx-4 bg-white rounded-lg dark:bg-gray-800 animate-fade-in-scale">
            <div class="flex items-center justify-between mb-4">
                <h3 class="text-lg font-semibold text-gray-900 dark:text-white">Upload File</h3>
                <button id="close-upload-modal" class="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="p-8 text-center border-2 border-gray-300 border-dashed rounded-lg dark:border-gray-600">
                <i class="mb-4 text-4xl text-gray-400 fas fa-cloud-upload-alt"></i>
                <p class="mb-2 text-gray-600 dark:text-gray-400">Drag and drop files here</p>
                <p class="mb-4 text-sm text-gray-500 dark:text-gray-500">or click to browse</p>
                <input type="file" id="file-input" class="hidden" multiple accept="image/*,.pdf,.doc,.docx,.txt">
                <button id="browse-files" class="px-4 py-2 text-white transition-colors rounded-lg bg-widdx-500 hover:bg-widdx-600">
                    Browse Files
                </button>
            </div>
            <div class="mt-4 text-xs text-gray-500 dark:text-gray-400">
                Supported: Images, PDF, DOC, DOCX, TXT (Max 10MB each)
            </div>
        </div>
    </div>

    <!-- Emoji Picker Modal -->
    <div id="emoji-modal" class="fixed inset-0 z-50 items-center justify-center hidden bg-black bg-opacity-50">
        <div class="w-full max-w-sm p-4 mx-4 bg-white rounded-lg dark:bg-gray-800 animate-fade-in-scale">
            <div class="flex items-center justify-between mb-4">
                <h3 class="text-lg font-semibold text-gray-900 dark:text-white">Add Emoji</h3>
                <button id="close-emoji-modal" class="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="grid grid-cols-8 gap-2 overflow-y-auto max-h-64 custom-scrollbar">
                <!-- Emojis will be populated here -->
            </div>
        </div>
    </div>

    <!-- Settings Modal -->
    <div id="settings-modal" class="fixed inset-0 z-50 items-center justify-center hidden bg-black bg-opacity-50">
        <div class="w-full max-w-md p-6 mx-4 bg-white rounded-lg dark:bg-gray-800 animate-fade-in-scale">
            <div class="flex items-center justify-between mb-6">
                <h3 class="text-xl font-semibold text-gray-900 dark:text-white">Settings</h3>
                <button id="close-settings-modal" class="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300">
                    <i class="fas fa-times"></i>
                </button>
            </div>

            <div class="space-y-6">
                <!-- Theme Settings -->
                <div>
                    <h4 class="mb-3 text-sm font-medium text-gray-900 dark:text-white">Appearance</h4>
                    <div class="flex items-center justify-between">
                        <span class="text-sm text-gray-700 dark:text-gray-300">Dark Mode</span>
                        <button id="theme-toggle-modal" class="relative inline-flex items-center h-6 transition-colors bg-gray-200 rounded-full w-11 dark:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-widdx-500 focus:ring-offset-2">
                            <span class="inline-block w-4 h-4 transition-transform transform bg-white rounded-full dark:translate-x-6"></span>
                        </button>
                    </div>
                </div>

                <!-- Language Settings -->
                <div>
                    <h4 class="mb-3 text-sm font-medium text-gray-900 dark:text-white">Language</h4>
                    <select id="language-select-modal" class="w-full p-2 text-gray-900 bg-white border border-gray-300 rounded-lg dark:border-gray-600 dark:bg-gray-700 dark:text-white">
                        <option value="en">🇺🇸 English</option>
                        <option value="ar">🇸🇦 العربية</option>
                        <option value="es">🇪🇸 Español</option>
                        <option value="fr">🇫🇷 Français</option>
                        <option value="de">🇩🇪 Deutsch</option>
                        <option value="zh">🇨🇳 中文</option>
                        <option value="ja">🇯🇵 日本語</option>
                        <option value="ko">🇰🇷 한국어</option>
                        <option value="ru">🇷🇺 Русский</option>
                    </select>
                </div>

                <!-- Interface Settings -->
                <div>
                    <h4 class="mb-3 text-sm font-medium text-gray-900 dark:text-white">Interface</h4>
                    <div class="space-y-3">
                        <div class="flex items-center justify-between">
                            <span class="text-sm text-gray-700 dark:text-gray-300">Sound Effects</span>
                            <input type="checkbox" id="sound-effects-toggle" class="border-gray-300 rounded text-widdx-600 focus:ring-widdx-500">
                        </div>
                        <div class="flex items-center justify-between">
                            <span class="text-sm text-gray-700 dark:text-gray-300">Animations</span>
                            <input type="checkbox" id="animations-toggle" class="border-gray-300 rounded text-widdx-600 focus:ring-widdx-500" checked>
                        </div>
                        <div class="flex items-center justify-between">
                            <span class="text-sm text-gray-700 dark:text-gray-300">Auto-save Drafts</span>
                            <input type="checkbox" id="autosave-toggle" class="border-gray-300 rounded text-widdx-600 focus:ring-widdx-500" checked>
                        </div>
                    </div>
                </div>

                <!-- Actions -->
                <div class="flex pt-4 space-x-3 border-t border-gray-200 dark:border-gray-600">
                    <button id="reset-settings" class="flex-1 px-4 py-2 text-sm text-gray-700 transition-colors bg-gray-100 rounded-lg dark:text-gray-300 dark:bg-gray-700 hover:bg-gray-200 dark:hover:bg-gray-600">
                        Reset to Default
                    </button>
                    <button id="save-settings" class="flex-1 px-4 py-2 text-sm text-white transition-colors rounded-lg bg-widdx-500 hover:bg-widdx-600">
                        Save Changes
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Notification Container -->
    <div id="notification-container" class="fixed z-50 space-y-2 top-4 right-4"></div>

    <!-- Progress Bar -->
    <div id="progress-container" class="fixed top-0 left-0 right-0 z-50 hidden">
        <div class="progress-bar" style="width: 0%"></div>
    </div>

    <!-- Scripts -->
    <script>
        // Global configuration
        window.WIDDX_CONFIG = {
            apiUrl: '<?php echo e(config("app.url")); ?>',
            csrfToken: '<?php echo e(csrf_token()); ?>',
            currentLanguage: 'en',
            features: {
                search: false,
                deepSearch: false,
                thinkMode: false,
                imageGeneration: false,
                vision: false
            }
        };

        // Debug logging
        console.log('🔧 WIDDX Config loaded:', window.WIDDX_CONFIG);
    </script>

    <!-- Main WIDDX Interface Script -->
    <!-- WIDDX Simple - Working Version -->
    <script src="<?php echo e(asset('js/widdx-simple.js')); ?>" defer></script>

    <!-- Simple Working Script -->
    <script>
        console.log('🚀 Simple WIDDX loading...');

        document.addEventListener('DOMContentLoaded', function() {
            console.log('🔧 DOM loaded, setting up simple WIDDX...');

            // Always setup our simple version
            setTimeout(() => {
                setupSimpleWIDDX();
            }, 1000);
        });

        function setupSimpleWIDDX() {
            console.log('🚨 Setting up simple WIDDX...');

            const sendButton = document.getElementById('send-button');
            const messageInput = document.getElementById('message-input');

            if (sendButton && messageInput) {
                // Clear any existing listeners
                sendButton.replaceWith(sendButton.cloneNode(true));
                messageInput.replaceWith(messageInput.cloneNode(true));

                // Get fresh references
                const newSendButton = document.getElementById('send-button');
                const newMessageInput = document.getElementById('message-input');

                // Add our listeners
                newSendButton.addEventListener('click', simpleSendMessage);
                newMessageInput.addEventListener('keydown', (e) => {
                    if (e.key === 'Enter' && !e.shiftKey) {
                        e.preventDefault();
                        simpleSendMessage();
                    }
                });

                // Update send button state
                newMessageInput.addEventListener('input', () => {
                    newSendButton.disabled = !newMessageInput.value.trim();
                });

                console.log('✅ Simple WIDDX setup complete');
                showSimpleNotification('WIDDX AI is ready! 🚀');
            } else {
                console.error('❌ Required elements not found');
            }
        }

        async function simpleSendMessage() {
            console.log('📤 Intelligent send message with auto features');

            const messageInput = document.getElementById('message-input');
            const messagesContainer = document.getElementById('messages-container');

            if (!messageInput || !messagesContainer) {
                console.error('❌ Required elements not found');
                return;
            }

            const message = messageInput.value.trim();
            if (!message) {
                console.warn('⚠️ Empty message');
                return;
            }

            console.log('📤 Sending:', message);

            // Clear input
            messageInput.value = '';
            document.getElementById('send-button').disabled = true;

            // Add user message
            addSimpleMessage(message, 'user');

            // Show thinking
            showSimpleThinking();

            try {
                const isArabic = /[\u0600-\u06FF]/.test(message);

                // Get manual features (only the 3 manual ones)
                const manualFeatures = getManualFeatures();

                console.log('🎛️ Manual features:', manualFeatures);

                const response = await fetch('/api/chat', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Accept': 'application/json'
                    },
                    body: JSON.stringify({
                        message: message,
                        language: isArabic ? 'ar' : 'en',
                        features: manualFeatures
                    })
                });

                console.log('📡 Response status:', response.status);

                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}`);
                }

                const result = await response.json();
                console.log('✅ Response received successfully');

                hideSimpleThinking();

                if (result.success) {
                    addSimpleMessage(result.message, 'assistant');

                    // Show which features were used (without revealing technical details)
                    if (result.auto_detected_features && Object.keys(result.auto_detected_features).length > 0) {
                        const autoFeatures = Object.keys(result.auto_detected_features).join(', ');
                        showSimpleNotification(`✨ Auto-activated: ${autoFeatures}`, 'info');
                    }

                } else {
                    addSimpleMessage('Error: ' + (result.error || 'Unknown error'), 'assistant');
                }

            } catch (error) {
                console.error('❌ Error:', error);
                hideSimpleThinking();

                const isArabic = /[\u0600-\u06FF]/.test(message);
                const fallback = isArabic
                    ? 'مرحباً! أنا WIDDX AI. كيف يمكنني مساعدتك؟'
                    : 'Hello! I\'m WIDDX AI. How can I help you?';
                addSimpleMessage(fallback, 'assistant');
            }
        }

        // Get only manual features (the 3 that require user activation)
        function getManualFeatures() {
            const manualFeatures = {};
            const manualFeaturesList = ['thinkMode', 'deepSearch', 'ultraDeepSearch'];

            manualFeaturesList.forEach(feature => {
                const toggle = document.querySelector(`[data-feature="${feature}"]`);
                if (toggle && toggle.classList.contains('active')) {
                    manualFeatures[feature] = true;
                }
            });

            return manualFeatures;
        }

        function addSimpleMessage(content, sender) {
            const messagesContainer = document.getElementById('messages-container');
            if (!messagesContainer) {
                console.error('❌ Messages container not found');
                return;
            }

            // Hide welcome message with animation
            const welcomeMessage = document.getElementById('welcome-message');
            if (welcomeMessage) {
                welcomeMessage.style.animation = 'fadeOut 0.3s ease-out forwards';
                setTimeout(() => {
                    welcomeMessage.style.display = 'none';
                }, 300);
            }

            const messageDiv = document.createElement('div');
            messageDiv.className = 'mb-6 max-w-3xl mx-auto animate-slide-up';

            const isUser = sender === 'user';
            const alignClass = isUser ? 'justify-end' : 'justify-start';

            messageDiv.innerHTML = `
                <div class="flex ${alignClass}">
                    <div class="flex items-start space-x-3 max-w-2xl ${isUser ? 'flex-row-reverse space-x-reverse' : ''}">
                        <div class="w-8 h-8 rounded-full flex items-center justify-center flex-shrink-0 ${
                            isUser ? 'bg-gray-300 dark:bg-gray-600' : 'bg-gradient-to-br from-widdx-500 to-widdx-600'
                        }">
                            <span class="text-sm font-bold ${isUser ? 'text-gray-700 dark:text-gray-300' : 'text-white'}">
                                ${isUser ? 'U' : 'W'}
                            </span>
                        </div>
                        <div class="flex-1 min-w-0">
                            <div class="p-4 rounded-lg ${
                                isUser
                                    ? 'bg-widdx-500 text-white'
                                    : 'bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700'
                            }">
                                <div class="text-sm ${isUser ? 'text-white' : 'text-gray-900 dark:text-white'}">
                                    ${content}
                                </div>
                            </div>
                            <div class="mt-1 text-xs text-gray-500 dark:text-gray-400 ${isUser ? 'text-right' : 'text-left'}">
                                ${new Date().toLocaleTimeString()}
                            </div>
                        </div>
                    </div>
                </div>
            `;

            messagesContainer.appendChild(messageDiv);
            messagesContainer.scrollTop = messagesContainer.scrollHeight;

            console.log(`💬 Added ${sender} message:`, content);
        }

        function showSimpleThinking() {
            const thinkingIndicator = document.getElementById('thinking-indicator');
            if (thinkingIndicator) {
                thinkingIndicator.classList.remove('hidden');
            }
        }

        function hideSimpleThinking() {
            const thinkingIndicator = document.getElementById('thinking-indicator');
            if (thinkingIndicator) {
                thinkingIndicator.classList.add('hidden');
            }
        }

        function showSimpleNotification(message, type = 'success') {
            const notification = document.createElement('div');

            const colors = {
                success: 'bg-green-500',
                info: 'bg-blue-500',
                warning: 'bg-yellow-500',
                error: 'bg-red-500'
            };

            notification.className = `fixed top-4 right-4 z-50 p-4 rounded-lg shadow-lg ${colors[type] || colors.success} text-white`;
            notification.textContent = message;
            document.body.appendChild(notification);

            setTimeout(() => {
                if (notification.parentElement) {
                    notification.remove();
                }
            }, type === 'info' ? 4000 : 3000);
        }

        // Settings Modal Functions (for inline modal if needed)
        function setupSettingsModal() {
            const settingsModal = document.getElementById('settings-modal');
            const closeSettingsModal = document.getElementById('close-settings-modal');
            const saveSettings = document.getElementById('save-settings');
            const resetSettings = document.getElementById('reset-settings');

            if (closeSettingsModal && settingsModal) {
                closeSettingsModal.addEventListener('click', () => {
                    settingsModal.classList.add('hidden');
                    settingsModal.classList.remove('flex');
                });
            }

            // Close modal when clicking outside
            if (settingsModal) {
                settingsModal.addEventListener('click', (e) => {
                    if (e.target === settingsModal) {
                        settingsModal.classList.add('hidden');
                        settingsModal.classList.remove('flex');
                    }
                });
            }

            if (saveSettings) {
                saveSettings.addEventListener('click', () => {
                    saveUserSettings();
                });
            }

            if (resetSettings) {
                resetSettings.addEventListener('click', () => {
                    resetUserSettings();
                });
            }
        }

        // Theme Toggle Functions
        function setupThemeToggle() {
            const themeToggle = document.getElementById('theme-toggle');

            if (themeToggle) {
                themeToggle.addEventListener('click', () => {
                    toggleTheme();
                });
            }
        }

        function toggleTheme() {
            const html = document.documentElement;
            const isDark = html.classList.contains('dark');

            if (isDark) {
                html.classList.remove('dark');
                localStorage.setItem('theme', 'light');
            } else {
                html.classList.add('dark');
                localStorage.setItem('theme', 'dark');
            }

            showSimpleNotification(isDark ? 'Light mode activated' : 'Dark mode activated', 'info');
        }

        // Feature Toggle Functions
        function setupFeatureToggles() {
            const featureToggles = document.querySelectorAll('.feature-toggle');

            featureToggles.forEach(toggle => {
                toggle.addEventListener('click', (e) => {
                    e.preventDefault();
                    const feature = toggle.dataset.feature;
                    if (feature) {
                        toggleFeature(feature, toggle);
                    }
                });
            });
        }

        function toggleFeature(feature, toggleElement) {
            const isActive = toggleElement.classList.contains('active');

            if (isActive) {
                toggleElement.classList.remove('active');
                toggleElement.classList.remove('bg-widdx-500', 'text-white');
                toggleElement.classList.add('bg-gray-100', 'dark:bg-gray-700', 'text-gray-700', 'dark:text-gray-300');
                showSimpleNotification(`${feature} deactivated`, 'info');
            } else {
                toggleElement.classList.add('active');
                toggleElement.classList.add('bg-widdx-500', 'text-white');
                toggleElement.classList.remove('bg-gray-100', 'dark:bg-gray-700', 'text-gray-700', 'dark:text-gray-300');
                showSimpleNotification(`${feature} activated`, 'success');
            }

            console.log(`Feature ${feature}: ${isActive ? 'OFF' : 'ON'}`);
        }

        // Settings Functions
        function saveUserSettings() {
            const settings = {
                theme: localStorage.getItem('theme') || 'dark',
                language: document.getElementById('language-select')?.value || 'en',
                notifications: document.getElementById('notifications-toggle')?.checked || true,
                autoSave: document.getElementById('auto-save-toggle')?.checked || true,
                timestamp: new Date().toISOString()
            };

            localStorage.setItem('widdx_settings', JSON.stringify(settings));
            showSimpleNotification('Settings saved successfully!', 'success');

            // Close modal
            const settingsModal = document.getElementById('settings-modal');
            if (settingsModal) {
                settingsModal.classList.add('hidden');
                settingsModal.classList.remove('flex');
            }
        }

        function resetUserSettings() {
            if (confirm('Are you sure you want to reset all settings to default?')) {
                localStorage.removeItem('widdx_settings');
                localStorage.setItem('theme', 'dark');

                // Reset form values
                const languageSelect = document.getElementById('language-select');
                if (languageSelect) languageSelect.value = 'en';

                const notificationsToggle = document.getElementById('notifications-toggle');
                if (notificationsToggle) notificationsToggle.checked = true;

                const autoSaveToggle = document.getElementById('auto-save-toggle');
                if (autoSaveToggle) autoSaveToggle.checked = true;

                // Apply dark theme
                document.documentElement.classList.add('dark');

                showSimpleNotification('Settings reset to default!', 'info');
            }
        }

        function loadUserSettings() {
            const savedSettings = localStorage.getItem('widdx_settings');
            if (savedSettings) {
                try {
                    const settings = JSON.parse(savedSettings);

                    // Apply theme
                    if (settings.theme === 'light') {
                        document.documentElement.classList.remove('dark');
                    } else {
                        document.documentElement.classList.add('dark');
                    }

                    // Apply language
                    const languageSelect = document.getElementById('language-select');
                    if (languageSelect && settings.language) {
                        languageSelect.value = settings.language;
                    }

                    // Apply other settings
                    const notificationsToggle = document.getElementById('notifications-toggle');
                    if (notificationsToggle) notificationsToggle.checked = settings.notifications !== false;

                    const autoSaveToggle = document.getElementById('auto-save-toggle');
                    if (autoSaveToggle) autoSaveToggle.checked = settings.autoSave !== false;

                } catch (error) {
                    console.error('Error loading settings:', error);
                }
            }
        }

        // Enhanced animations and interactions
        function addDynamicEffects() {
            // Add hover effects to all buttons
            document.querySelectorAll('button').forEach(button => {
                button.addEventListener('mouseenter', () => {
                    button.style.transform = 'translateY(-1px)';
                });
                button.addEventListener('mouseleave', () => {
                    button.style.transform = 'translateY(0)';
                });
            });

            // Add typing effect to placeholders
            const messageInput = document.getElementById('message-input');
            if (messageInput) {
                const placeholders = [
                    'Ask WIDDX anything...',
                    'Try: "Draw me a cat"',
                    'Try: "Translate: Hello"',
                    'Try: "Search for latest news"',
                    'Try: "Write a Python function"'
                ];
                let currentIndex = 0;

                setInterval(() => {
                    currentIndex = (currentIndex + 1) % placeholders.length;
                    messageInput.placeholder = placeholders[currentIndex];
                }, 3000);
            }

            // Add particle effects on feature activation
            function createParticle(x, y) {
                const particle = document.createElement('div');
                particle.className = 'fixed w-2 h-2 bg-widdx-500 rounded-full pointer-events-none z-50';
                particle.style.left = x + 'px';
                particle.style.top = y + 'px';
                particle.style.animation = 'float 1s ease-out forwards';
                document.body.appendChild(particle);

                setTimeout(() => {
                    particle.remove();
                }, 1000);
            }

            // Enhanced feature toggle effects
            document.querySelectorAll('.feature-toggle').forEach(toggle => {
                toggle.addEventListener('click', (e) => {
                    const rect = toggle.getBoundingClientRect();
                    const x = rect.left + rect.width / 2;
                    const y = rect.top + rect.height / 2;

                    // Create particles
                    for (let i = 0; i < 5; i++) {
                        setTimeout(() => {
                            createParticle(x + (Math.random() - 0.5) * 20, y + (Math.random() - 0.5) * 20);
                        }, i * 100);
                    }
                });
            });

            // Add smooth scrolling to chat
            const chatContainer = document.getElementById('chat-messages');
            if (chatContainer) {
                chatContainer.style.scrollBehavior = 'smooth';
            }

            // Add focus effects to input
            if (messageInput) {
                messageInput.addEventListener('focus', () => {
                    messageInput.parentElement.classList.add('animate-glow');
                });
                messageInput.addEventListener('blur', () => {
                    messageInput.parentElement.classList.remove('animate-glow');
                });
            }
        }

        // Initialize everything when DOM is ready
        document.addEventListener('DOMContentLoaded', function() {
            console.log('🚀 Initializing WIDDX interface...');

            // Load saved settings first
            loadUserSettings();

            // Setup all components
            setupSimpleWIDDX();
            setupSettingsModal();
            setupThemeToggle();
            setupFeatureToggles();

            // Add dynamic effects
            addDynamicEffects();

            console.log('✅ WIDDX interface initialized successfully');
        });
    </script>






</body>
</html>
<?php /**PATH C:\Users\<USER>\Desktop\widdx-ai\resources\views/widdx-modern.blade.php ENDPATH**/ ?>