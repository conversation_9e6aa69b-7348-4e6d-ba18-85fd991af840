/**
 * Final Test - Test all fixes are working
 */

console.log('🎯 Final Test Starting...');

async function runFinalTest() {
    console.log('🚀 Running Final Test Suite...');
    console.log('================================');
    
    const results = {
        apiHealth: false,
        messageSending: false,
        arabicResponse: false,
        englishResponse: false,
        themeToggle: false,
        settingsModal: false,
        enterKey: false
    };
    
    // Test 1: API Health
    console.log('1️⃣ Testing API Health...');
    try {
        const response = await fetch('/api/chat/health');
        const data = await response.json();
        if (response.ok && data.status === 'ok') {
            results.apiHealth = true;
            console.log('✅ API Health: PASS');
        } else {
            console.log('❌ API Health: FAIL');
        }
    } catch (error) {
        console.log('❌ API Health: ERROR -', error.message);
    }
    
    // Test 2: Message Sending Function
    console.log('2️⃣ Testing Message Sending Function...');
    if (window.widdxModern && typeof window.widdxModern.submitMessage === 'function') {
        results.messageSending = true;
        console.log('✅ Message Sending Function: PASS');
    } else {
        console.log('❌ Message Sending Function: FAIL');
    }
    
    // Test 3: Arabic Response
    console.log('3️⃣ Testing Arabic Response...');
    try {
        const response = await fetch('/api/chat', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'Accept': 'application/json'
            },
            body: JSON.stringify({
                message: 'مرحبا',
                features: {},
                language: 'ar'
            })
        });
        
        const data = await response.json();
        if (response.ok && data.success && data.message.includes('مرحباً')) {
            results.arabicResponse = true;
            console.log('✅ Arabic Response: PASS -', data.message.substring(0, 50) + '...');
        } else {
            console.log('❌ Arabic Response: FAIL -', data);
        }
    } catch (error) {
        console.log('❌ Arabic Response: ERROR -', error.message);
    }
    
    // Test 4: English Response
    console.log('4️⃣ Testing English Response...');
    try {
        const response = await fetch('/api/chat', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'Accept': 'application/json'
            },
            body: JSON.stringify({
                message: 'hello',
                features: {},
                language: 'en'
            })
        });
        
        const data = await response.json();
        if (response.ok && data.success && data.message.includes('Hello')) {
            results.englishResponse = true;
            console.log('✅ English Response: PASS -', data.message.substring(0, 50) + '...');
        } else {
            console.log('❌ English Response: FAIL -', data);
        }
    } catch (error) {
        console.log('❌ English Response: ERROR -', error.message);
    }
    
    // Test 5: Theme Toggle
    console.log('5️⃣ Testing Theme Toggle...');
    const themeToggle = document.getElementById('theme-toggle');
    if (themeToggle) {
        const initialDark = document.documentElement.classList.contains('dark');
        themeToggle.click();
        
        setTimeout(() => {
            const newDark = document.documentElement.classList.contains('dark');
            if (newDark !== initialDark) {
                results.themeToggle = true;
                console.log('✅ Theme Toggle: PASS');
                // Toggle back
                themeToggle.click();
            } else {
                console.log('❌ Theme Toggle: FAIL');
            }
        }, 100);
    } else {
        console.log('❌ Theme Toggle: FAIL - Button not found');
    }
    
    // Test 6: Settings Modal
    console.log('6️⃣ Testing Settings Modal...');
    const settingsToggle = document.getElementById('settings-toggle');
    const settingsModal = document.getElementById('settings-modal');
    
    if (settingsToggle && settingsModal) {
        settingsToggle.click();
        
        setTimeout(() => {
            if (!settingsModal.classList.contains('hidden')) {
                results.settingsModal = true;
                console.log('✅ Settings Modal: PASS');
                
                // Close it
                const closeButton = document.getElementById('close-settings-modal');
                if (closeButton) {
                    closeButton.click();
                }
            } else {
                console.log('❌ Settings Modal: FAIL - Did not open');
            }
        }, 100);
    } else {
        console.log('❌ Settings Modal: FAIL - Elements not found');
    }
    
    // Test 7: Enter Key
    console.log('7️⃣ Testing Enter Key...');
    const messageInput = document.getElementById('message-input');
    if (messageInput) {
        messageInput.value = 'Test Enter';
        messageInput.focus();
        
        const enterEvent = new KeyboardEvent('keydown', {
            key: 'Enter',
            bubbles: true,
            cancelable: true
        });
        
        const result = messageInput.dispatchEvent(enterEvent);
        results.enterKey = true;
        console.log('✅ Enter Key: PASS - Event dispatched');
    } else {
        console.log('❌ Enter Key: FAIL - Input not found');
    }
    
    // Wait for async tests to complete
    setTimeout(() => {
        console.log('\n🏁 FINAL TEST RESULTS');
        console.log('======================');
        
        let passed = 0;
        const total = Object.keys(results).length;
        
        Object.entries(results).forEach(([test, result]) => {
            const status = result ? '✅ PASS' : '❌ FAIL';
            console.log(`${status} ${test}`);
            if (result) passed++;
        });
        
        const percentage = ((passed / total) * 100).toFixed(1);
        console.log(`\n🎯 Overall Score: ${passed}/${total} (${percentage}%)`);
        
        if (passed === total) {
            console.log('🎉 ALL TESTS PASSED! WIDDX is working perfectly!');
            if (window.widdxModern && typeof window.widdxModern.showNotification === 'function') {
                window.widdxModern.showNotification('🎉 All systems working!', 'success');
            }
        } else if (passed >= total * 0.8) {
            console.log('✅ Most tests passed! WIDDX is mostly working.');
            if (window.widdxModern && typeof window.widdxModern.showNotification === 'function') {
                window.widdxModern.showNotification(`${passed}/${total} tests passed`, 'success');
            }
        } else {
            console.log('⚠️ Some tests failed. Check the issues above.');
            if (window.widdxModern && typeof window.widdxModern.showNotification === 'function') {
                window.widdxModern.showNotification(`${passed}/${total} tests passed`, 'warning');
            }
        }
        
        // Show specific instructions
        console.log('\n📋 To test manually:');
        console.log('1. Type "مرحبا" and press Enter - should respond in Arabic');
        console.log('2. Type "hello" and press Enter - should respond in English');
        console.log('3. Click the moon/sun icon - should toggle theme');
        console.log('4. Click the gear icon - should open settings');
        console.log('5. Press Enter in message input - should send message');
        
    }, 2000);
    
    return results;
}

// Auto-run final test after a delay
setTimeout(() => {
    runFinalTest();
}, 3000);

// Export for manual testing
window.runFinalTest = runFinalTest;
