/**
 * WIDDX AI - Comprehensive Button Testing Script
 * اختبار شامل لجميع أزرار الواجهة
 */

class ButtonTester {
    constructor() {
        this.testResults = [];
        this.totalTests = 0;
        this.passedTests = 0;
        this.failedTests = 0;
        
        console.log('🧪 بدء اختبار شامل للأزرار...');
        this.runAllTests();
    }

    log(message, type = 'info') {
        const timestamp = new Date().toLocaleTimeString();
        const emoji = type === 'success' ? '✅' : type === 'error' ? '❌' : type === 'warning' ? '⚠️' : 'ℹ️';
        console.log(`${emoji} [${timestamp}] ${message}`);
        
        this.testResults.push({
            timestamp,
            message,
            type
        });
    }

    test(description, testFunction) {
        this.totalTests++;
        try {
            const result = testFunction();
            if (result) {
                this.passedTests++;
                this.log(`PASS: ${description}`, 'success');
                return true;
            } else {
                this.failedTests++;
                this.log(`FAIL: ${description}`, 'error');
                return false;
            }
        } catch (error) {
            this.failedTests++;
            this.log(`ERROR: ${description} - ${error.message}`, 'error');
            return false;
        }
    }

    runAllTests() {
        this.log('بدء الاختبارات الشاملة للواجهة');

        // Test 1: Feature Toggle Buttons
        this.testFeatureToggles();
        
        // Test 2: Chat Form
        this.testChatForm();
        
        // Test 3: Sidebar Buttons
        this.testSidebarButtons();
        
        // Test 4: Input Actions
        this.testInputActions();
        
        // Test 5: Settings Buttons
        this.testSettingsButtons();
        
        // Test 6: Theme and Language
        this.testThemeAndLanguage();
        
        // Test 7: Keyboard Shortcuts
        this.testKeyboardShortcuts();
        
        // Test 8: Event Listeners
        this.testEventListeners();

        // Generate Report
        this.generateReport();
    }

    testFeatureToggles() {
        this.log('اختبار أزرار تبديل الميزات...');

        // Test feature toggle elements exist
        this.test('وجود أزرار تبديل الميزات', () => {
            const toggles = document.querySelectorAll('.feature-toggle');
            return toggles.length >= 5;
        });

        // Test each feature toggle
        const features = ['search', 'deepSearch', 'thinkMode', 'imageGeneration', 'vision'];
        features.forEach(feature => {
            this.test(`وجود زر ${feature}`, () => {
                const toggle = document.querySelector(`[data-feature="${feature}"]`);
                return toggle !== null;
            });
        });

        // Test toggle functionality
        this.test('وظيفة تبديل الميزات', () => {
            const toggle = document.querySelector('.feature-toggle');
            if (!toggle) return false;
            
            const initialState = toggle.classList.contains('active');
            toggle.click();
            const newState = toggle.classList.contains('active');
            
            return initialState !== newState;
        });
    }

    testChatForm() {
        this.log('اختبار نموذج الدردشة...');

        this.test('وجود نموذج الدردشة', () => {
            return document.getElementById('chat-form') !== null;
        });

        this.test('وجود حقل الإدخال', () => {
            return document.getElementById('message-input') !== null;
        });

        this.test('وجود زر الإرسال', () => {
            return document.getElementById('send-button') !== null;
        });

        this.test('وجود عداد الأحرف', () => {
            return document.getElementById('char-count') !== null;
        });

        this.test('وظيفة تغيير حجم النص', () => {
            const input = document.getElementById('message-input');
            if (!input) return false;
            
            const initialHeight = input.style.height;
            input.value = 'نص طويل\nسطر جديد\nسطر آخر';
            input.dispatchEvent(new Event('input'));
            
            return input.style.height !== initialHeight;
        });
    }

    testSidebarButtons() {
        this.log('اختبار أزرار الشريط الجانبي...');

        const sidebarButtons = [
            'new-chat-btn',
            'reset-features', 
            'search-chats',
            'clear-history',
            'settings-toggle',
            'theme-toggle'
        ];

        sidebarButtons.forEach(buttonId => {
            this.test(`وجود زر ${buttonId}`, () => {
                return document.getElementById(buttonId) !== null;
            });
        });
    }

    testInputActions() {
        this.log('اختبار أزرار الإدخال الإضافية...');

        const inputButtons = [
            'attach-file',
            'voice-input', 
            'emoji-picker',
            'clear-input'
        ];

        inputButtons.forEach(buttonId => {
            this.test(`وجود زر ${buttonId}`, () => {
                return document.getElementById(buttonId) !== null;
            });
        });

        // Test modals
        this.test('وجود نافذة رفع الملفات', () => {
            return document.getElementById('file-upload-modal') !== null;
        });

        this.test('وجود نافذة الإيموجي', () => {
            return document.getElementById('emoji-modal') !== null;
        });
    }

    testSettingsButtons() {
        this.log('اختبار أزرار الإعدادات...');

        const settingsToggles = [
            'voice-input-toggle',
            'sound-effects-toggle',
            'animations-toggle',
            'compact-mode-toggle',
            'save-history-toggle',
            'analytics-toggle'
        ];

        settingsToggles.forEach(toggleId => {
            this.test(`وجود إعداد ${toggleId}`, () => {
                return document.getElementById(toggleId) !== null;
            });
        });
    }

    testThemeAndLanguage() {
        this.log('اختبار المظهر واللغة...');

        this.test('وجود محدد اللغة', () => {
            return document.getElementById('language-select') !== null;
        });

        this.test('وجود زر تبديل المظهر', () => {
            return document.getElementById('theme-toggle') !== null;
        });

        this.test('وظيفة تبديل المظهر', () => {
            const themeToggle = document.getElementById('theme-toggle');
            if (!themeToggle) return false;
            
            const initialTheme = document.documentElement.classList.contains('dark');
            themeToggle.click();
            const newTheme = document.documentElement.classList.contains('dark');
            
            return initialTheme !== newTheme;
        });
    }

    testKeyboardShortcuts() {
        this.log('اختبار اختصارات لوحة المفاتيح...');

        this.test('اختصار Ctrl+Enter للإرسال', () => {
            const input = document.getElementById('message-input');
            if (!input) return false;
            
            input.value = 'اختبار';
            const event = new KeyboardEvent('keydown', {
                key: 'Enter',
                ctrlKey: true
            });
            
            document.dispatchEvent(event);
            return true; // If no error thrown, test passes
        });

        this.test('اختصارات Alt+Number للميزات', () => {
            const event = new KeyboardEvent('keydown', {
                key: '1',
                altKey: true
            });
            
            document.dispatchEvent(event);
            return true; // If no error thrown, test passes
        });
    }

    testEventListeners() {
        this.log('اختبار مستمعي الأحداث...');

        this.test('مستمع نموذج الدردشة', () => {
            const form = document.getElementById('chat-form');
            if (!form) return false;
            
            const listeners = getEventListeners ? getEventListeners(form) : null;
            return listeners ? listeners.submit && listeners.submit.length > 0 : true;
        });

        this.test('مستمعي أزرار الميزات', () => {
            const toggles = document.querySelectorAll('.feature-toggle');
            return toggles.length > 0; // If toggles exist, assume listeners are attached
        });
    }

    generateReport() {
        this.log('إنشاء تقرير الاختبار...');

        const successRate = ((this.passedTests / this.totalTests) * 100).toFixed(1);
        
        console.log('\n' + '='.repeat(50));
        console.log('📊 تقرير اختبار الأزرار الشامل');
        console.log('='.repeat(50));
        console.log(`📈 إجمالي الاختبارات: ${this.totalTests}`);
        console.log(`✅ نجح: ${this.passedTests}`);
        console.log(`❌ فشل: ${this.failedTests}`);
        console.log(`📊 معدل النجاح: ${successRate}%`);
        console.log('='.repeat(50));

        if (this.failedTests === 0) {
            console.log('🎉 جميع الاختبارات نجحت! الواجهة تعمل بشكل ممتاز.');
        } else if (successRate >= 80) {
            console.log('👍 معظم الاختبارات نجحت. هناك بعض المشاكل الطفيفة.');
        } else {
            console.log('⚠️ هناك مشاكل تحتاج إلى إصلاح.');
        }

        // Store results globally for access
        window.buttonTestResults = {
            total: this.totalTests,
            passed: this.passedTests,
            failed: this.failedTests,
            successRate: successRate,
            details: this.testResults
        };

        return {
            success: this.failedTests === 0,
            successRate: successRate,
            results: this.testResults
        };
    }
}

// Auto-run tests when script loads
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', () => {
        setTimeout(() => new ButtonTester(), 1000);
    });
} else {
    setTimeout(() => new ButtonTester(), 1000);
}

// Export for manual testing
window.ButtonTester = ButtonTester;
