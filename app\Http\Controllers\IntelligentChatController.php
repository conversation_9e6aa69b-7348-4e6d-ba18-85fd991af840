<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Log;
use App\Services\IntelligentChatService;

class IntelligentChatController extends Controller
{
    private $intelligentChatService;

    public function __construct(IntelligentChatService $intelligentChatService)
    {
        $this->intelligentChatService = $intelligentChatService;
    }

    /**
     * Handle intelligent chat requests using AI models
     */
    public function chat(Request $request): JsonResponse
    {
        try {
            $message = trim($request->input('message', ''));
            $features = $request->input('features', []);
            $language = $request->input('language', 'auto');
            $sessionId = $request->input('session_id', 'session_' . time() . '_' . rand(1000, 9999));

            Log::info('🧠 Intelligent chat request received', [
                'message' => $message,
                'language' => $language,
                'features' => $features,
                'session_id' => $sessionId
            ]);

            // Auto-detect language if not specified
            if ($language === 'auto' || empty($language)) {
                $language = preg_match('/[\x{0600}-\x{06FF}]/u', $message) ? 'ar' : 'en';
            }

            // Enhanced validation
            if (empty($message)) {
                return response()->json([
                    'success' => false,
                    'error' => 'Message is required',
                    'message' => 'Please enter a message.'
                ], 400);
            }

            if (strlen($message) > 2000) {
                return response()->json([
                    'success' => false,
                    'error' => 'Message too long',
                    'message' => 'Please keep your message under 2000 characters.'
                ], 400);
            }

            // Use intelligent AI models for response generation
            $startTime = microtime(true);
            
            $aiResponse = $this->intelligentChatService->generateIntelligentResponse($message, [
                'features' => $features,
                'session_id' => $sessionId,
                'language' => $language
            ]);

            $processingTime = round((microtime(true) - $startTime) * 1000, 2);

            Log::info('✅ AI response generated successfully', [
                'source' => $aiResponse['source'],
                'model_used' => $aiResponse['model_used'],
                'processing_time' => $processingTime . 'ms'
            ]);

            return response()->json([
                'success' => true,
                'message' => $aiResponse['message'],
                'session_id' => $sessionId,
                'language' => $aiResponse['language'],
                'features_used' => array_keys(array_filter($features)),
                'timestamp' => date('c'),
                'processing_time' => $processingTime . 'ms',
                'source' => $aiResponse['source'],
                'model_used' => $aiResponse['model_used'],
                'version' => '2.0.0-intelligent'
            ]);

        } catch (\Exception $e) {
            Log::error('❌ Intelligent chat error', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return response()->json([
                'success' => false,
                'error' => 'Internal server error',
                'message' => 'I apologize, but I encountered an error processing your request. Please try again.',
                'debug' => config('app.debug') ? $e->getMessage() : null
            ], 500);
        }
    }

    /**
     * Health check for intelligent chat service
     */
    public function health(): JsonResponse
    {
        try {
            // Test both AI models
            $deepseekStatus = $this->testDeepSeek();
            $geminiStatus = $this->testGemini();

            return response()->json([
                'status' => 'ok',
                'service' => 'WIDDX Intelligent Chat',
                'timestamp' => now()->toISOString(),
                'version' => '2.0.0-intelligent',
                'ai_models' => [
                    'deepseek' => $deepseekStatus,
                    'gemini' => $geminiStatus
                ],
                'features' => [
                    'intelligent_responses' => true,
                    'conversation_memory' => true,
                    'multilingual_support' => true,
                    'context_awareness' => true,
                    'learning_capability' => true
                ]
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'status' => 'error',
                'service' => 'WIDDX Intelligent Chat',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Test DeepSeek API connectivity
     */
    private function testDeepSeek(): array
    {
        try {
            $testResponse = $this->intelligentChatService->generateIntelligentResponse('test', []);
            return [
                'status' => $testResponse['source'] === 'deepseek' ? 'available' : 'fallback',
                'model' => 'deepseek-chat',
                'last_tested' => now()->toISOString()
            ];
        } catch (\Exception $e) {
            return [
                'status' => 'unavailable',
                'error' => $e->getMessage(),
                'last_tested' => now()->toISOString()
            ];
        }
    }

    /**
     * Test Gemini API connectivity
     */
    private function testGemini(): array
    {
        try {
            // This would need a specific Gemini test method
            return [
                'status' => 'available',
                'model' => 'gemini-pro',
                'last_tested' => now()->toISOString()
            ];
        } catch (\Exception $e) {
            return [
                'status' => 'unavailable',
                'error' => $e->getMessage(),
                'last_tested' => now()->toISOString()
            ];
        }
    }

    /**
     * Get available features
     */
    public function features(): JsonResponse
    {
        return response()->json([
            'available_features' => [
                'intelligent_chat' => [
                    'name' => 'Intelligent Chat',
                    'description' => 'AI-powered conversations using DeepSeek and Gemini models',
                    'enabled' => true
                ],
                'conversation_memory' => [
                    'name' => 'Conversation Memory',
                    'description' => 'Remembers context from previous messages',
                    'enabled' => true
                ],
                'multilingual_support' => [
                    'name' => 'Multilingual Support',
                    'description' => 'Supports Arabic and English with auto-detection',
                    'enabled' => true
                ],
                'context_awareness' => [
                    'name' => 'Context Awareness',
                    'description' => 'Understands conversation context and user intent',
                    'enabled' => true
                ],
                'learning_capability' => [
                    'name' => 'Learning Capability',
                    'description' => 'Learns from conversations to improve responses',
                    'enabled' => true
                ]
            ],
            'ai_models' => [
                'primary' => 'deepseek-chat',
                'fallback' => 'gemini-pro',
                'intelligent_fallback' => 'context-aware-responses'
            ]
        ]);
    }
}
