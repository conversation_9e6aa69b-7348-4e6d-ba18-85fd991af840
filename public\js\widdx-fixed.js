/**
 * WIDDX AI - Fixed Version
 * This is a clean, working version that definitely connects to the API
 */

console.log('🚀 WIDDX Fixed Version Loading...');

class WiddxFixed {
    constructor() {
        this.isTyping = false;
        this.currentSession = null;
        this.features = {
            search: false,
            deepSearch: false,
            thinkMode: false,
            imageGeneration: false,
            vision: false
        };
        this.config = {
            currentLanguage: 'auto'
        };
        
        console.log('✅ WIDDX Fixed initialized');
        this.init();
    }

    init() {
        console.log('🔧 Setting up WIDDX Fixed...');
        this.setupEventListeners();
        console.log('✅ WIDDX Fixed ready!');
        
        // Show ready notification
        setTimeout(() => {
            this.showNotification('WIDDX Fixed is ready! 🚀', 'success');
        }, 1000);
    }

    setupEventListeners() {
        // Send button
        const sendButton = document.getElementById('send-button');
        if (sendButton) {
            sendButton.addEventListener('click', (e) => {
                e.preventDefault();
                this.submitMessage();
            });
        }

        // Enter key
        const messageInput = document.getElementById('message-input');
        if (messageInput) {
            messageInput.addEventListener('keydown', (e) => {
                if (e.key === 'Enter' && !e.shiftKey) {
                    e.preventDefault();
                    this.submitMessage();
                }
            });
        }

        console.log('✅ Event listeners set up');
    }

    async submitMessage() {
        console.log('📤 Submit message called');
        
        const messageInput = document.getElementById('message-input');
        if (!messageInput) {
            console.error('❌ Message input not found');
            return;
        }

        const message = messageInput.value.trim();
        if (!message || this.isTyping) {
            console.warn('⚠️ Empty message or already typing');
            return;
        }

        console.log('📝 Message to send:', message);

        // Clear input
        messageInput.value = '';
        this.updateSendButton();

        // Add user message
        this.addMessage(message, 'user');

        // Show thinking
        this.showThinking();

        try {
            // Prepare request
            const requestData = {
                message: message,
                features: this.features,
                language: this.config.currentLanguage,
                session_id: this.currentSession
            };

            console.log('🚀 Sending request:', requestData);

            // Send to API
            const response = await this.sendToAPI(requestData);

            console.log('✅ Got response:', response);

            // Hide thinking
            this.hideThinking();

            // Add response
            this.addMessage(response.message || 'Response received!', 'assistant', response);

            // Update session
            if (response.session_id) {
                this.currentSession = response.session_id;
            }

        } catch (error) {
            console.error('❌ Error in submitMessage:', error);
            this.hideThinking();
            this.addMessage('Sorry, I encountered an error. Please try again.', 'assistant', { error: true });
        }
    }

    async sendToAPI(data) {
        console.log('🚀 sendToAPI called with:', data);
        
        try {
            console.log('📡 Making fetch request to /api/chat');
            
            const response = await fetch('/api/chat', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Accept': 'application/json',
                    'X-Requested-With': 'XMLHttpRequest'
                },
                body: JSON.stringify(data)
            });

            console.log('📡 Response status:', response.status);
            console.log('📡 Response ok:', response.ok);

            if (!response.ok) {
                const errorText = await response.text();
                console.error('❌ API Error Response:', errorText);
                throw new Error(`HTTP ${response.status}: ${errorText}`);
            }

            const result = await response.json();
            console.log('✅ API Response parsed:', result);
            
            if (!result.success) {
                console.error('❌ API returned unsuccessful response:', result);
                throw new Error(result.error || 'API returned unsuccessful response');
            }
            
            return result;
            
        } catch (error) {
            console.error('❌ sendToAPI failed:', error);
            console.warn('🔄 Using fallback...');
            
            // Fallback response
            const isArabic = data.language === 'ar' || /[\u0600-\u06FF]/.test(data.message);
            const message = data.message.toLowerCase();
            
            let response = '';
            if (isArabic) {
                if (message.includes('مرحبا') || message.includes('السلام') || message.includes('أهلا')) {
                    response = 'مرحباً بك! أنا WIDDX AI، مساعدك الذكي. كيف يمكنني مساعدتك اليوم؟';
                } else {
                    response = 'شكراً لك على رسالتك! أنا WIDDX AI وأنا هنا لمساعدتك.';
                }
            } else {
                if (message.includes('hello') || message.includes('hi')) {
                    response = 'Hello! I\'m WIDDX AI, your intelligent assistant. How can I help you today?';
                } else {
                    response = 'Thank you for your message! I\'m WIDDX AI and I\'m here to help you.';
                }
            }

            return {
                success: true,
                message: response,
                session_id: 'fallback_' + Date.now(),
                language: isArabic ? 'ar' : 'en',
                source: 'fallback'
            };
        }
    }

    addMessage(content, sender, metadata = {}) {
        console.log('💬 Adding message:', { content, sender, metadata });
        
        const messagesContainer = document.getElementById('messages-container');
        if (!messagesContainer) {
            console.error('❌ Messages container not found');
            return;
        }

        const messageElement = this.createMessageElement(content, sender, metadata);
        messagesContainer.appendChild(messageElement);
        this.scrollToBottom();
    }

    createMessageElement(content, sender, metadata = {}) {
        const messageDiv = document.createElement('div');
        messageDiv.className = 'animate-slide-in mb-6 max-w-3xl mx-auto';

        const isUser = sender === 'user';
        const alignClass = isUser ? 'justify-end' : 'justify-start';

        messageDiv.innerHTML = `
            <div class="flex ${alignClass}">
                <div class="flex items-start space-x-3 max-w-2xl ${isUser ? 'flex-row-reverse space-x-reverse' : ''}">
                    <div class="w-8 h-8 rounded-full flex items-center justify-center flex-shrink-0 ${
                        isUser
                            ? 'bg-gray-300 dark:bg-gray-600'
                            : 'bg-gradient-to-br from-widdx-500 to-widdx-600'
                    }">
                        <span class="text-sm font-bold ${isUser ? 'text-gray-700 dark:text-gray-300' : 'text-white'}">
                            ${isUser ? 'U' : 'W'}
                        </span>
                    </div>
                    <div class="flex-1 min-w-0">
                        <div class="p-4 rounded-lg ${
                            isUser
                                ? 'bg-widdx-500 text-white'
                                : 'bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700'
                        }">
                            <div class="text-sm ${isUser ? 'text-white' : 'text-gray-900 dark:text-white'}">
                                ${content}
                            </div>
                        </div>
                        <div class="mt-1 text-xs text-gray-500 dark:text-gray-400 ${isUser ? 'text-right' : 'text-left'}">
                            ${new Date().toLocaleTimeString()}
                            ${metadata.source ? ` • ${metadata.source}` : ''}
                        </div>
                    </div>
                </div>
            </div>
        `;

        return messageDiv;
    }

    showThinking() {
        console.log('🤔 Showing thinking indicator');
        this.isTyping = true;
        const thinkingIndicator = document.getElementById('thinking-indicator');
        if (thinkingIndicator) {
            thinkingIndicator.classList.remove('hidden');
            this.scrollToBottom();
        }
    }

    hideThinking() {
        console.log('✅ Hiding thinking indicator');
        this.isTyping = false;
        const thinkingIndicator = document.getElementById('thinking-indicator');
        if (thinkingIndicator) {
            thinkingIndicator.classList.add('hidden');
        }
    }

    updateSendButton() {
        const messageInput = document.getElementById('message-input');
        const sendButton = document.getElementById('send-button');
        
        if (messageInput && sendButton) {
            const hasText = messageInput.value.trim().length > 0;
            sendButton.disabled = !hasText || this.isTyping;
        }
    }

    scrollToBottom() {
        const messagesContainer = document.getElementById('messages-container');
        if (messagesContainer) {
            messagesContainer.scrollTop = messagesContainer.scrollHeight;
        }
    }

    showNotification(message, type = 'info') {
        console.log(`📢 Notification (${type}):`, message);
        
        // Create notification element
        const notification = document.createElement('div');
        notification.className = `fixed top-4 right-4 p-4 rounded-lg shadow-lg z-50 ${
            type === 'success' ? 'bg-green-500 text-white' :
            type === 'error' ? 'bg-red-500 text-white' :
            type === 'warning' ? 'bg-yellow-500 text-black' :
            'bg-blue-500 text-white'
        }`;
        notification.textContent = message;
        
        document.body.appendChild(notification);
        
        // Remove after 3 seconds
        setTimeout(() => {
            if (notification.parentNode) {
                notification.parentNode.removeChild(notification);
            }
        }, 3000);
    }
}

// Initialize when DOM is ready
document.addEventListener('DOMContentLoaded', () => {
    console.log('🚀 DOM ready, initializing WIDDX Fixed...');
    window.widdxFixed = new WiddxFixed();
});

console.log('📄 WIDDX Fixed script loaded');
