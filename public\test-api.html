<!DOCTYPE html>
<html>
<head>
    <title>Test WIDDX API</title>
    <meta charset="utf-8">
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .container { max-width: 800px; margin: 0 auto; }
        .test-section { margin: 20px 0; padding: 20px; border: 1px solid #ddd; border-radius: 5px; }
        .result { margin: 10px 0; padding: 10px; background: #f5f5f5; border-radius: 3px; }
        .success { background: #d4edda; color: #155724; }
        .error { background: #f8d7da; color: #721c24; }
        button { padding: 10px 20px; margin: 5px; cursor: pointer; }
        input[type="text"] { width: 300px; padding: 5px; margin: 5px; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧪 WIDDX API Test</h1>
        
        <div class="test-section">
            <h2>1. Health Check</h2>
            <button onclick="testHealth()">Test Health</button>
            <div id="health-result" class="result"></div>
        </div>
        
        <div class="test-section">
            <h2>2. Chat API</h2>
            <input type="text" id="message-input" placeholder="Enter message..." value="مرحبا">
            <button onclick="testChat()">Send Message</button>
            <div id="chat-result" class="result"></div>
        </div>
        
        <div class="test-section">
            <h2>3. Features API</h2>
            <button onclick="testFeatures()">Get Features</button>
            <div id="features-result" class="result"></div>
        </div>
    </div>

    <script>
        async function testHealth() {
            const resultDiv = document.getElementById('health-result');
            resultDiv.innerHTML = 'Testing...';
            
            try {
                const response = await fetch('/api/chat/health');
                const data = await response.json();
                
                if (response.ok) {
                    resultDiv.className = 'result success';
                    resultDiv.innerHTML = `✅ Health Check Passed<br><pre>${JSON.stringify(data, null, 2)}</pre>`;
                } else {
                    resultDiv.className = 'result error';
                    resultDiv.innerHTML = `❌ Health Check Failed: ${response.status}<br><pre>${JSON.stringify(data, null, 2)}</pre>`;
                }
            } catch (error) {
                resultDiv.className = 'result error';
                resultDiv.innerHTML = `❌ Error: ${error.message}`;
            }
        }
        
        async function testChat() {
            const resultDiv = document.getElementById('chat-result');
            const messageInput = document.getElementById('message-input');
            const message = messageInput.value.trim();
            
            if (!message) {
                resultDiv.className = 'result error';
                resultDiv.innerHTML = '❌ Please enter a message';
                return;
            }
            
            resultDiv.innerHTML = 'Sending message...';
            
            try {
                const response = await fetch('/api/chat', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Accept': 'application/json'
                    },
                    body: JSON.stringify({
                        message: message,
                        features: {
                            search: false,
                            deepSearch: false,
                            thinkMode: false,
                            imageGeneration: false,
                            vision: false
                        },
                        language: 'ar'
                    })
                });
                
                const data = await response.json();
                
                if (response.ok && data.success) {
                    resultDiv.className = 'result success';
                    resultDiv.innerHTML = `✅ Chat Response Received<br><strong>WIDDX:</strong> ${data.message}<br><pre>${JSON.stringify(data, null, 2)}</pre>`;
                } else {
                    resultDiv.className = 'result error';
                    resultDiv.innerHTML = `❌ Chat Failed: ${response.status}<br><pre>${JSON.stringify(data, null, 2)}</pre>`;
                }
            } catch (error) {
                resultDiv.className = 'result error';
                resultDiv.innerHTML = `❌ Error: ${error.message}`;
            }
        }
        
        async function testFeatures() {
            const resultDiv = document.getElementById('features-result');
            resultDiv.innerHTML = 'Getting features...';
            
            try {
                const response = await fetch('/api/chat/features');
                const data = await response.json();
                
                if (response.ok) {
                    resultDiv.className = 'result success';
                    resultDiv.innerHTML = `✅ Features Retrieved<br><pre>${JSON.stringify(data, null, 2)}</pre>`;
                } else {
                    resultDiv.className = 'result error';
                    resultDiv.innerHTML = `❌ Features Failed: ${response.status}<br><pre>${JSON.stringify(data, null, 2)}</pre>`;
                }
            } catch (error) {
                resultDiv.className = 'result error';
                resultDiv.innerHTML = `❌ Error: ${error.message}`;
            }
        }
        
        // Auto-run health check on load
        window.addEventListener('load', () => {
            testHealth();
        });
    </script>
</body>
</html>
