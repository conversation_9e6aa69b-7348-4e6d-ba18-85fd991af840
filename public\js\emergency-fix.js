/**
 * Emergency Fix - Force API to work correctly
 */

console.log('🚨 Emergency Fix Starting...');

// Override the submitMessage function to force API usage
function emergencyFixSubmitMessage() {
    if (window.widdxModern) {
        console.log('🔧 Applying emergency fix to submitMessage...');
        
        // Store original function
        const originalSubmitMessage = window.widdxModern.submitMessage;
        
        // Override with fixed version
        window.widdxModern.submitMessage = async function() {
            const messageInput = document.getElementById('message-input');
            if (!messageInput) {
                console.error('❌ Message input not found');
                return;
            }

            const message = messageInput.value.trim();
            if (!message) {
                console.warn('⚠️ Empty message');
                return;
            }

            console.log('📤 Emergency Fix: Sending message:', message);

            // Clear input immediately
            messageInput.value = '';
            this.updateSendButton();

            // Add user message
            this.addMessage(message, 'user');

            // Show thinking
            this.showThinking();

            try {
                // Prepare request data
                const requestData = {
                    message: message,
                    features: this.features || {},
                    language: this.config?.currentLanguage || 'en',
                    session_id: this.currentSession || 'emergency_' + Date.now()
                };

                console.log('🚀 Emergency Fix: Request data:', requestData);

                // Force API call
                const response = await this.forceAPICall(requestData);

                // Hide thinking
                this.hideThinking();

                // Add response
                this.addMessage(response.message || 'Emergency response received!', 'assistant', response);

                console.log('✅ Emergency Fix: Message sent successfully');

            } catch (error) {
                console.error('❌ Emergency Fix: Error:', error);
                this.hideThinking();
                this.addMessage('Sorry, I encountered an error. Please try again.', 'assistant', { error: true });
            }
        };

        // Add forceAPICall method
        window.widdxModern.forceAPICall = async function(data) {
            console.log('🔥 Force API Call:', data);
            
            try {
                const response = await fetch('/api/chat', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Accept': 'application/json'
                    },
                    body: JSON.stringify(data)
                });

                console.log('📡 Force API Response Status:', response.status);

                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }

                const result = await response.json();
                console.log('✅ Force API Result:', result);

                if (result.success) {
                    return result;
                } else {
                    throw new Error(result.error || 'API returned unsuccessful response');
                }
            } catch (error) {
                console.error('❌ Force API Error:', error);
                
                // Emergency fallback
                const isArabic = data.language === 'ar' || /[\u0600-\u06FF]/.test(data.message);
                const message = data.message.toLowerCase();
                
                let response = '';
                if (isArabic) {
                    if (message.includes('مرحبا') || message.includes('السلام') || message.includes('أهلا')) {
                        response = 'مرحباً بك! أنا WIDDX AI، مساعدك الذكي. كيف يمكنني مساعدتك اليوم؟';
                    } else {
                        response = 'شكراً لك على رسالتك! أنا WIDDX AI وأنا هنا لمساعدتك.';
                    }
                } else {
                    if (message.includes('hello') || message.includes('hi')) {
                        response = 'Hello! I\'m WIDDX AI, your intelligent assistant. How can I help you today?';
                    } else {
                        response = 'Thank you for your message! I\'m WIDDX AI and I\'m here to help you.';
                    }
                }

                return {
                    success: true,
                    message: response,
                    session_id: 'emergency_' + Date.now(),
                    language: data.language
                };
            }
        };

        console.log('✅ Emergency fix applied successfully!');
        
        // Show notification
        if (typeof window.widdxModern.showNotification === 'function') {
            window.widdxModern.showNotification('Emergency fix applied! 🚨', 'info');
        }
        
        return true;
    } else {
        console.error('❌ WIDDX Modern instance not found');
        return false;
    }
}

// Apply emergency fix after a delay
setTimeout(() => {
    const success = emergencyFixSubmitMessage();
    if (success) {
        console.log('🎉 Emergency fix applied! Try sending a message now.');
    } else {
        console.error('❌ Emergency fix failed to apply');
    }
}, 3000);

// Export for manual use
window.emergencyFixSubmitMessage = emergencyFixSubmitMessage;
